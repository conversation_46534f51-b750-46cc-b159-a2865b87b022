# Deploy DEV-STRAPI-CMS Container

resources:
  repositories:
    - repository: 'PIPELINES-TEMPLATES'
      type: git
      name: SysOP/PIPELINES-TEMPLATES

variables:
  - template: vars-stg-strapicms-template.yml@PIPELINES-TEMPLATES
  

pool:
  vmImage: 'ubuntu-latest'


trigger:
  branches:
    include:
      - staging
  paths:
    exclude:
      - '**/*.yml'

jobs:
  - job: Build_Push_DockerImage
    steps:
      - template: securefile-template.yml@PIPELINES-TEMPLATES
        parameters:
          SecureFileName: 'DigiCertGlobalRootCA.crt.pem'
          SecureFilePath: '$(System.DefaultWorkingDirectory)'

      - template: securefile-template.yml@PIPELINES-TEMPLATES
        parameters:
          SecureFileName: '.env.stg'
          SecureFilePath: '$(System.DefaultWorkingDirectory)'
      
      - task: Bash@3
        inputs:
          targetType: 'inline'
          script: |
            mv .env.stg .env
          workingDirectory: '$(System.DefaultWorkingDirectory)'
      
      - template: buildandpush-image-template.yml@PIPELINES-TEMPLATES
        parameters:
          containerRegistry: $(containerRegistry)
          DockerRepository: $(DockerRepository)
          Tag: $(Tag)
         
  - job: 'Deploy_STRAPICMS_Container'
    dependsOn: Build_Push_DockerImage
    condition: succeeded()
    steps:
      - task: MicrosoftSecurityDevOps@1
        displayName: 'Microsoft Security DevOps'
       
      - template: deploy-non-prod-webapp-container-template.yml@PIPELINES-TEMPLATES
        parameters:
          ConnectedServiceName: '$(ConnectedServiceName)'
          DockerNamespace: '$(DockerNamespace)'
          DockerRepository: '$(DockerRepository)'
          WebAppName: '$(WebAppName)'
          Tag: '$(Tag)'