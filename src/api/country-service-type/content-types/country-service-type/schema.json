{"kind": "collectionType", "collectionName": "country_service_types", "info": {"singularName": "country-service-type", "pluralName": "country-service-types", "displayName": "CountryServiceType"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"active": {"type": "boolean", "default": false}, "country": {"type": "relation", "relation": "oneToOne", "target": "api::core-country.core-country"}, "service": {"type": "relation", "relation": "oneToOne", "target": "api::service-type.service-type"}}}