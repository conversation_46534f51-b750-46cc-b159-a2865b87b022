import { sanitizeCountryName } from "../../../../extensions/countryServicesUrls";

const partnerLifecycles = {
  async afterCreate(event: { result: any; params: any }) {
    const { result } = event;
    await createOrUpdateUrlsForCountry(result);
  },

  async afterUpdate(event: { result: any; params: any }) {
    const { result } = event;
    await createOrUpdateUrlsForCountry(result);
  }
};

const serviceMappings = {
  remittance: {
    pageType: 'RemittanceCountry',
  },
  mobiletopup: {
    pageType: 'Top-UpCountry',
  },
  giftcard: {
    pageType: 'GiftCardCountry',
  }
};

async function createOrUpdateUrlsForCountry(countryServiceType: any) {
  try {
    console.log(`Processing URLs for countryServiceType`, JSON.stringify(countryServiceType, null, 2));
    const currentCountryServiceType: any = await strapi.entityService
      .findOne('api::country-service-type.country-service-type', countryServiceType.id, {
        populate: [
          'country',
          'service.localized_suffixes'
        ]
      });
    console.log('currentCountryServiceType: ', JSON.stringify(currentCountryServiceType, null, 2));
    if (!currentCountryServiceType) {
      return;
    }

    const serviceType = currentCountryServiceType.service;
    if (!serviceType || !serviceType.name) {
      //TODO remove this countryservicetype because something is wrong
      return;
    }

    const country = currentCountryServiceType.country;
    if (!country || !country.codeAlpha2) {
      //TODO remove this countryservicetype because something is wrong
      return;
    }

    const languageService = strapi.plugin("i18n").service("locales");
    const availableLocales = await languageService.find();
    console.log('availableLocales: ', JSON.stringify(availableLocales, null, 2));

    const countriesByLocale = await Promise.all(
      availableLocales?.map(({ code }) => strapi.entityService.findMany('api::core-country.core-country', {
        filters: {
          codeAlpha2: country.codeAlpha2
        },
        locale: code
      }) || [])
    );
    console.log('countriesByLocale: ', JSON.stringify(countriesByLocale, null, 2));

    for (const index in availableLocales) {
      console.log('index: ', index);
      const locale = availableLocales[index];
      console.log('locale: ', locale.code);
      const countryByLocale = countriesByLocale?.[index]?.[0];
      console.log('countryByLocale: ', countryByLocale);
      await createOrUpdateUrl(countryByLocale, serviceType, locale.code);
    }
  } catch (error) {
    console.error('Error in createOrUpdateUrlsForCountry:', error);
  }
}

async function findUrlByCountryAndService(country: any, serviceType: any, locale: string) {
  try {

    const urlResponse = await strapi.entityService.findMany('api::url.url', {
      filters: {
        country: {
          codeAlpha2: country.codeAlpha2
        }
      },
      populate: ['country'],
      locale
    });
    console.log('urlResponse: ', JSON.stringify(urlResponse, null, 2));

    const urlFound = urlResponse.find(({ pageType }) => {
      const typeName = pageType.replace('Country', '').toLowerCase();
      if (typeName === serviceType.name.toLowerCase()) {
        return true;
      }
      if (typeName === 'top-up' && serviceType.name.toLowerCase() === 'mobiletopup') {
        return true;
      }
      return false;
    });

    return urlFound;
  } catch (error) {
    console.error('findUrlByCountryAndService error: ', error);
    return null
  }
}

function getBaseUrl(fullUrl: string): string {
  try {
    const url = new URL(fullUrl);
    return `${url.protocol}//${url.hostname}`;
  } catch (error) {
    throw new Error("Invalid URL");
  }
}

async function createOrUpdateUrl(country: any, serviceType: any, locale: string) {
  try {
    const urlFound = await findUrlByCountryAndService(country, serviceType, locale);
    console.log('urlFound: ', JSON.stringify(urlFound, null, 2));

    const urlBase = 'https://www.sendvalu.com';

    if (urlFound) {
      // await updateUrl(url, country, serviceType, locale);
    } else {
      const countryName = sanitizeCountryName(country.name);
      const serviceSuffix = serviceType.localized_suffixes.find((suffix) => suffix.locale === locale)?.suffix || `${serviceType.name}-to`;
      const suffixURL = `${serviceSuffix.toLowerCase()}-${countryName}`;
      const serviceName = serviceType.name.toLowerCase();
      const pageType = serviceMappings[serviceName]?.pageType || serviceName;
      console.log(`Creating URL for ${pageType}: prefix=${urlBase}, suffix=${suffixURL}, locale=${locale}`);

      const urlData = {
        pageType: pageType,
        country: country.id,
        prefixURL: urlBase,
        suffixURL: suffixURL,
        urlType: 'Referred',
        locale,
      };
      await strapi.entityService.create('api::url.url', {
        data: urlData
      } as any);

    }
  } catch (error) {
    console.error(`Error creating/updating URL for ${country.name} and ${serviceType.name}:`, error);
  }
}

async function removeUrlIfExists(country: any, pageType: string) {
  try {
    const existingUrl: any = await strapi.entityService.findMany('api::url.url', {
      filters: {
        pageType: pageType,
        country: country.id
      } as any
    });

    if (existingUrl && existingUrl.length > 0) {
      console.log(`Removing URL for ${pageType}: ${existingUrl[0].id}`);

      for (const url of existingUrl) {
        await strapi.entityService.delete('api::url.url', url.id);
      }
    }
  } catch (error) {
    console.error(`Error removing URL for ${pageType}:`, error);
  }
}

export default partnerLifecycles;
