{"kind": "collectionType", "collectionName": "sitemap_builds", "info": {"singularName": "sitemap-build", "pluralName": "sitemap-builds", "displayName": "Sitemap Build", "description": "Records of sitemap generation and rebuild activities"}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"buildId": {"type": "uid", "targetField": "buildName"}, "buildName": {"type": "string", "required": true, "unique": false}, "locale": {"type": "string"}, "status": {"type": "enumeration", "enum": ["pending", "in-progress", "completed", "failed"], "default": "pending", "required": true}, "buildType": {"type": "enumeration", "enum": ["manual", "automatic", "scheduled"], "default": "manual", "required": true}, "externalApiEndpoint": {"type": "string", "required": false}, "totalUrls": {"type": "integer", "default": 0, "min": 0}, "processedUrls": {"type": "integer", "default": 0, "min": 0}, "failedUrls": {"type": "integer", "default": 0, "min": 0}, "buildStartTime": {"type": "datetime", "required": false}, "buildEndTime": {"type": "datetime", "required": false}, "buildDuration": {"type": "integer", "required": false, "min": 0}, "sitemapUrls": {"type": "json", "required": false, "default": []}, "errorLog": {"type": "text", "required": false}, "buildLog": {"type": "text", "required": false}, "sitemapSize": {"type": "integer", "required": false, "min": 0}, "compressionEnabled": {"type": "boolean", "default": false}, "sitemapFormat": {"type": "enumeration", "enum": ["xml", "txt", "rss"], "default": "xml"}, "lastModified": {"type": "datetime", "required": false}, "changeFrequency": {"type": "enumeration", "enum": ["always", "hourly", "daily", "weekly", "monthly", "yearly", "never"], "default": "weekly"}, "priority": {"type": "decimal", "default": 0.5, "min": 0.0, "max": 1.0}, "includeImages": {"type": "boolean", "default": false}, "includeVideos": {"type": "boolean", "default": false}, "includeNews": {"type": "boolean", "default": false}, "locales": {"type": "json", "required": false, "default": []}, "robotsTxtUpdated": {"type": "boolean", "default": false}, "searchEnginesNotified": {"type": "json", "required": false, "default": []}, "buildMetadata": {"type": "json", "required": false, "default": {}}, "triggeredBy": {"type": "string", "required": false}, "buildVersion": {"type": "string", "required": false}}}