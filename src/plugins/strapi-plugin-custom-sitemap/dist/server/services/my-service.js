"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const axiosInstance_1 = require("../utils/axiosInstance");
const url_1 = require("../utils/url");
exports.default = ({ strapi }) => ({
    getWelcomeMessage() {
        return 'Welcome to Strapi 🚀';
    },
    async getSitemapData(locale = 'en') {
        try {
            if (!strapi.entityService) {
                throw new Error('Entity service not available');
            }
            // Get the latest completed sitemap build
            const latestBuild = await strapi.entityService.findMany('plugin::strapi-plugin-custom-sitemap.sitemap-build', {
                filters: {
                    status: 'completed',
                },
                sort: { createdAt: 'desc' },
                limit: 1,
            });
            const lastRebuildDate = latestBuild && latestBuild.length > 0 ? latestBuild[0].createdAt : null;
            // Get static content items that include in sitemap
            const filters = {
                includeInSitemap: true,
                locale,
            };
            if (lastRebuildDate) {
                filters.createdAt = {
                    $gt: lastRebuildDate,
                };
            }
            const staticContentItems = await strapi.entityService.findMany('api::static-content.static-content', {
                filters,
                populate: '*',
                sort: { createdAt: 'desc' },
                limit: 50,
            });
            return {
                lastRebuildDate,
                staticContentItems: staticContentItems || [],
                totalItems: staticContentItems ? staticContentItems.length : 0,
                latestBuild: latestBuild && latestBuild.length > 0 ? latestBuild[0] : null,
                locale
            };
        }
        catch (error) {
            strapi.log.error('Error fetching sitemap data:', error);
            throw error;
        }
    },
    async rebuildSitemap({ triggeredBy, locale = 'en' }) {
        var _a, _b, _c;
        console.log('rebuildSitemap locale: ', locale);
        if (!strapi.entityService) {
            throw new Error('Entity service not available');
        }
        const buildStartTime = new Date();
        let sitemapBuild;
        try {
            // Create a new sitemap build record
            sitemapBuild = await strapi.entityService.create('plugin::strapi-plugin-custom-sitemap.sitemap-build', {
                data: {
                    buildName: `Sitemap Build ${buildStartTime.toISOString()}`,
                    status: 'in-progress',
                    buildType: 'manual',
                    externalApiEndpoint: axiosInstance_1.WEBSITE_API_URL,
                    buildStartTime: buildStartTime.toISOString(),
                    triggeredBy: triggeredBy || 'admin-dashboard',
                    buildVersion: '1.0.0',
                    locale,
                }, // Workaround: 'as any' due to Strapi plugin type limitations
            });
            // Get all static content items that should be included in sitemap
            const staticContentItems = await strapi.entityService.findMany('api::static-content.static-content', {
                filters: {
                    includeInSitemap: true,
                    publishedAt: {
                        $notNull: true,
                    },
                },
                locale,
                populate: '*',
                fields: ['url']
            });
            const staticContentArray = staticContentItems || [];
            console.log('staticContentArray length: ', staticContentArray.length);
            // Prepare sitemap data according to XML Sitemap Protocol
            const sitemapData = staticContentArray
                .filter(({ url }) => (0, url_1.isValidUrl)(url))
                .map((item) => {
                console.log('url: ', item.url);
                return {
                    loc: item.url,
                    lastmod: item.updatedAt,
                    changefreq: 'weekly',
                    priority: 0.8,
                    // Additional sitemap fields can be added here
                    images: [],
                    videos: [],
                    news: null, // News sitemap data if needed
                };
            });
            console.log('sitemapData length: ', sitemapData.length);
            // Update build record with URL count
            await strapi.entityService.update('plugin::strapi-plugin-custom-sitemap.sitemap-build', sitemapBuild.id, {
                data: {
                    totalUrls: sitemapData.length,
                    sitemapUrls: sitemapData,
                    locale
                }, // Workaround: 'as any' due to Strapi plugin type limitations
            });
            let response = null;
            try {
                // Call external API to rebuild sitemap
                response = await axiosInstance_1.webAxiosinstance.post('/replace-sitemap', {
                    [locale]: sitemapData.map(({ loc }) => loc),
                });
                console.log('/replace-sitemap response: ', response);
            }
            catch (error) {
                console.error(error);
                response = error.response;
            }
            const buildEndTime = new Date();
            const buildDuration = buildEndTime.getTime() - buildStartTime.getTime();
            if (!((_a = response === null || response === void 0 ? void 0 : response.data) === null || _a === void 0 ? void 0 : _a.success)) {
                // Update build record as failed
                await strapi.entityService.update('plugin::strapi-plugin-custom-sitemap.sitemap-build', sitemapBuild.id, {
                    data: {
                        status: 'failed',
                        buildEndTime: buildEndTime.toISOString(),
                        buildDuration,
                        errorLog: `External API responded with status: ${(_b = response === null || response === void 0 ? void 0 : response.data) === null || _b === void 0 ? void 0 : _b.message}`,
                        failedUrls: sitemapData.length,
                    }, // Workaround: 'as any' due to Strapi plugin type limitations
                });
                throw new Error(`External API responded with status: ${(_c = response === null || response === void 0 ? void 0 : response.data) === null || _c === void 0 ? void 0 : _c.message}`);
            }
            // Update build record as completed
            await strapi.entityService.update('plugin::strapi-plugin-custom-sitemap.sitemap-build', sitemapBuild.id, {
                data: {
                    status: 'completed',
                    buildEndTime: buildEndTime.toISOString(),
                    buildDuration,
                    processedUrls: sitemapData.length,
                    lastModified: buildEndTime.toISOString(),
                    buildLog: `Successfully processed ${sitemapData.length} URLs`,
                }, // Workaround: 'as any' due to Strapi plugin type limitations
            });
            return {
                success: true,
                rebuildDate: buildEndTime.toISOString(),
                itemsProcessed: sitemapData.length,
                message: 'Sitemap rebuilt successfully',
                buildId: sitemapBuild.id,
                buildDuration,
                locale
            };
        }
        catch (error) {
            strapi.log.error('Error rebuilding sitemap:', error);
            // Update build record as failed if it exists
            if (sitemapBuild) {
                const buildEndTime = new Date();
                const buildDuration = buildEndTime.getTime() - buildStartTime.getTime();
                await strapi.entityService.update('plugin::strapi-plugin-custom-sitemap.sitemap-build', sitemapBuild.id, {
                    data: {
                        status: 'failed',
                        buildEndTime: buildEndTime.toISOString(),
                        buildDuration,
                        errorLog: error instanceof Error ? error.message : 'Unknown error occurred',
                        locale
                    }, // Workaround: 'as any' due to Strapi plugin type limitations
                });
            }
            throw error;
        }
    },
    async getStaticContentStats(locale = 'en') {
        try {
            if (!strapi.entityService) {
                throw new Error('Entity service not available');
            }
            const totalItems = await strapi.entityService.count('api::static-content.static-content', {
                locale
            });
            const includedItems = await strapi.entityService.count('api::static-content.static-content', {
                filters: {
                    includeInSitemap: true,
                },
                locale,
            });
            const publishedIncludedItems = await strapi.entityService.count('api::static-content.static-content', {
                filters: {
                    includeInSitemap: true,
                    publishedAt: {
                        $notNull: true,
                    },
                },
                locale,
            });
            return {
                totalItems,
                includedItems,
                publishedIncludedItems,
                excludedItems: totalItems - includedItems,
            };
        }
        catch (error) {
            strapi.log.error('Error fetching static content stats:', error);
            throw error;
        }
    },
    async getSitemapBuilds(limit = 10, locale = 'en') {
        try {
            if (!strapi.entityService) {
                throw new Error('Entity service not available');
            }
            const builds = await strapi.entityService.findMany('plugin::strapi-plugin-custom-sitemap.sitemap-build', {
                sort: { createdAt: 'desc' },
                limit,
                filters: { locale, }
            });
            return builds || [];
        }
        catch (error) {
            strapi.log.error('Error fetching sitemap builds:', error);
            throw error;
        }
    },
    async getSitemapBuildById(id) {
        try {
            if (!strapi.entityService) {
                throw new Error('Entity service not available');
            }
            const build = await strapi.entityService.findOne('plugin::strapi-plugin-custom-sitemap.sitemap-build', id);
            return build;
        }
        catch (error) {
            strapi.log.error('Error fetching sitemap build:', error);
            throw error;
        }
    },
});
