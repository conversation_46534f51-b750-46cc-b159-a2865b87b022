"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.webAxiosinstance = exports.WEBSITE_API_URL = void 0;
/**
 * axios with a custom config for web page.
 */
const axios_1 = __importDefault(require("axios"));
exports.WEBSITE_API_URL = process.env.WEBSITE_API_URL || 'https://homepagedev.sendvalu.com/api'; // Replace with actual URL
const WEBSITE_API_PASS_KEY = process.env.WEBSITE_API_PASS_KEY || 'isNotBearer-weAreBear';
exports.webAxiosinstance = axios_1.default.create({
    baseURL: exports.WEBSITE_API_URL,
    headers: {
        'team-pass-key': WEBSITE_API_PASS_KEY,
        Accept: 'application/json',
        'Content-Type': 'application/json',
    }
});
