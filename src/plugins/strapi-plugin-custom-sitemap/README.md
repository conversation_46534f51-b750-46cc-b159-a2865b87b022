# Strapi Custom Sitemap Plugin

A comprehensive Strapi plugin for managing website sitemaps with an intuitive admin dashboard. This plugin allows you to monitor static content, rebuild sitemaps via external APIs, and track sitemap generation statistics.

## Features

- **Admin Dashboard**: Clean, intuitive interface for sitemap management
- **Statistics Overview**: Real-time stats on content inclusion in sitemap
- **External API Integration**: Rebuild sitemaps by calling external endpoints
- **Content Monitoring**: Track static content items with `includeInSitemap=true`
- **Rebuild Tracking**: Monitor last rebuild dates and newly created content
- **TypeScript Support**: Fully typed for better development experience

## Installation

This plugin is already installed in your Strapi application. Make sure it's enabled in your `config/plugins.ts`:

```typescript
export default {
  'strapi-plugin-custom-sitemap': {
    enabled: true,
    resolve: './src/plugins/strapi-plugin-custom-sitemap'
  },
}
```

## Usage

### Admin Dashboard

1. Navigate to the plugin in your Strapi admin panel
2. View statistics about your static content
3. Monitor which items are included/excluded from the sitemap
4. Track the last sitemap rebuild date

### Rebuilding Sitemap

1. Enter your external API endpoint in the dashboard
2. Click "Rebuild Sitemap" to send current sitemap data to your external service
3. The plugin will send a POST request with the following structure:

```json
{
  "urls": [
    {
      "url": "/page-url",
      "lastModified": "2024-01-01T00:00:00.000Z",
      "changeFreq": "weekly",
      "priority": 0.8
    }
  ],
  "timestamp": "2024-01-01T00:00:00.000Z"
}
```

### Static Content Requirements

For content to be included in the sitemap, it must:
- Have `includeInSitemap` set to `true`
- Be published (`publishedAt` is not null)
- Be of type `static-content`

## API Endpoints

The plugin exposes the following REST endpoints:

- `GET /strapi-plugin-custom-sitemap/sitemap-data` - Get sitemap data and recent content
- `GET /strapi-plugin-custom-sitemap/stats` - Get content statistics
- `POST /strapi-plugin-custom-sitemap/rebuild-sitemap` - Rebuild sitemap via external API

## Configuration

### External API Integration

Your external API endpoint should accept POST requests with the sitemap data structure shown above. The endpoint should:

1. Accept the sitemap data
2. Generate/update your website's sitemap
3. Return a success response (2xx status code)

### Content Types

The plugin works with two main content types:

#### Static Content (`api::static-content.static-content`)
- `includeInSitemap` (boolean) - Whether to include in sitemap
- `url` (string) - The page URL
- `referenceName` (string) - Display name for the content
- `country` (string, optional) - Content localization
- Standard Strapi fields (`createdAt`, `updatedAt`, `publishedAt`)

#### Sitemap Build (`plugin::strapi-plugin-custom-sitemap.sitemap-build`)
A comprehensive collection that tracks all sitemap generation activities with the following fields:

**Basic Information:**
- `buildName` (string) - Human-readable name for the build
- `buildId` (uid) - Unique identifier for the build
- `status` (enum) - pending, in-progress, completed, failed
- `buildType` (enum) - manual, automatic, scheduled

**Build Configuration:**
- `externalApiEndpoint` (string) - The API endpoint used for rebuild
- `sitemapFormat` (enum) - xml, txt, rss (default: xml)
- `changeFrequency` (enum) - always, hourly, daily, weekly, monthly, yearly, never
- `priority` (decimal) - Default priority for URLs (0.0-1.0)
- `compressionEnabled` (boolean) - Whether compression is enabled

**URL Processing:**
- `totalUrls` (integer) - Total number of URLs to process
- `processedUrls` (integer) - Successfully processed URLs
- `failedUrls` (integer) - Failed URL processing count
- `sitemapUrls` (json) - Array of processed URL objects with sitemap data

**Timing & Performance:**
- `buildStartTime` (datetime) - When the build started
- `buildEndTime` (datetime) - When the build completed
- `buildDuration` (integer) - Build duration in milliseconds
- `lastModified` (datetime) - Last modification timestamp

**Advanced Features:**
- `includeImages` (boolean) - Include image sitemap data
- `includeVideos` (boolean) - Include video sitemap data
- `includeNews` (boolean) - Include news sitemap data
- `locales` (json) - Array of supported locales
- `robotsTxtUpdated` (boolean) - Whether robots.txt was updated
- `searchEnginesNotified` (json) - Array of notified search engines

**Logging & Metadata:**
- `buildLog` (text) - Success messages and build information
- `errorLog` (text) - Error messages and debugging information
- `buildMetadata` (json) - Additional metadata about the build
- `triggeredBy` (string) - Who or what triggered the build
- `buildVersion` (string) - Version of the build system used
- `sitemapSize` (integer) - Final sitemap file size in bytes

This comprehensive tracking system allows you to:
- Monitor sitemap generation history
- Debug failed builds
- Track performance metrics
- Maintain audit trails
- Support advanced sitemap features (images, videos, news)
- Handle multi-language sitemaps

## Development

### File Structure

```
admin/src/
├── components/
│   ├── StatsCards.tsx          # Statistics display cards
│   ├── RebuildSitemapCard.tsx  # Sitemap rebuild interface
│   ├── StaticContentTable.tsx  # Content listing table
│   └── index.ts               # Component exports
├── pages/
│   └── HomePage/              # Main dashboard page
├── types/
│   └── index.ts              # TypeScript interfaces
├── utils/
│   ├── api.ts                # API request utilities
│   └── useSitemapData.ts     # Custom React hook
└── translations/
    └── en.json               # English translations

server/
├── controllers/
│   └── my-controller.ts      # API endpoints
├── services/
│   └── my-service.ts         # Business logic
└── routes/
    └── index.ts              # Route definitions
```

### Adding New Features

1. **New API Endpoints**: Add to `server/controllers/my-controller.ts` and `server/routes/index.ts`
2. **New Services**: Extend `server/services/my-service.ts`
3. **UI Components**: Add to `admin/src/components/`
4. **Types**: Update `admin/src/types/index.ts`

## Troubleshooting

### Common Issues

1. **External API not responding**: Check your endpoint URL and ensure it accepts POST requests
2. **No content showing**: Verify that static content items have `includeInSitemap: true` and are published
3. **Plugin not loading**: Ensure the plugin is properly enabled in `config/plugins.ts`

### Debug Mode

Enable debug logging by checking the browser console and Strapi server logs for detailed error messages.

## License

This plugin is part of your Strapi application and follows the same licensing terms.
