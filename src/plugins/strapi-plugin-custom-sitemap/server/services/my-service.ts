import { Strapi } from '@strapi/strapi';
import { webAxiosinstance, WEBSITE_API_URL } from '../utils/axiosInstance'
import { isValidUrl } from '../utils/url';

export default ({ strapi }: { strapi: Strapi }) => ({
  getWelcomeMessage() {
    return 'Welcome to Strapi 🚀';
  },

  async getSitemapData(locale: string = 'en') {
    try {
      if (!strapi.entityService) {
        throw new Error('Entity service not available');
      }

      // Get the latest completed sitemap build
      const latestBuild = await strapi.entityService.findMany('plugin::strapi-plugin-custom-sitemap.sitemap-build', {
        filters: {
          status: 'completed',
        },
        sort: { createdAt: 'desc' },
        limit: 1,
      });

      const lastRebuildDate = latestBuild && latestBuild.length > 0 ? latestBuild[0].createdAt : null;

      // Get static content items that include in sitemap
      const filters: any = {
        includeInSitemap: true,
        locale,
      };

      if (lastRebuildDate) {
        filters.createdAt = {
          $gt: lastRebuildDate,
        };
      }

      const staticContentItems = await strapi.entityService.findMany('api::static-content.static-content', {
        filters,
        populate: '*',
        sort: { createdAt: 'desc' },
        limit: 50,
      });

      return {
        lastRebuildDate,
        staticContentItems: staticContentItems || [],
        totalItems: staticContentItems ? staticContentItems.length : 0,
        latestBuild: latestBuild && latestBuild.length > 0 ? latestBuild[0] : null,
        locale
      };
    } catch (error) {
      strapi.log.error('Error fetching sitemap data:', error);
      throw error;
    }
  },

  async rebuildSitemap({ triggeredBy, locale = 'en' }: { triggeredBy?: string, locale: string }) {
    console.log('rebuildSitemap locale: ', locale);
    if (!strapi.entityService) {
      throw new Error('Entity service not available');
    }

    const buildStartTime = new Date();
    let sitemapBuild;

    try {
      // Create a new sitemap build record
      sitemapBuild = await strapi.entityService.create('plugin::strapi-plugin-custom-sitemap.sitemap-build', {
        data: {
          buildName: `Sitemap Build ${buildStartTime.toISOString()}`,
          status: 'in-progress',
          buildType: 'manual',
          externalApiEndpoint: WEBSITE_API_URL,
          buildStartTime: buildStartTime.toISOString(),
          triggeredBy: triggeredBy || 'admin-dashboard',
          buildVersion: '1.0.0',
          locale,
        } as any, // Workaround: 'as any' due to Strapi plugin type limitations
      });

      // Get all static content items that should be included in sitemap
      const staticContentItems = await strapi.entityService.findMany('api::static-content.static-content', {
        filters: {
          includeInSitemap: true,
          publishedAt: {
            $notNull: true,
          },
        },
        locale,
        populate: '*',
        fields: ['url']
      });
      const staticContentArray = staticContentItems || [];
      console.log('staticContentArray length: ', staticContentArray.length);

      // Prepare sitemap data according to XML Sitemap Protocol
      const sitemapData = staticContentArray
        .filter(({ url }: any) => isValidUrl(url))
        .map((item: any) => {
          console.log('url: ', item.url);
          return {
            loc: item.url, // URL of the page (required)
            lastmod: item.updatedAt, // Last modification date
            changefreq: 'weekly', // How frequently the page is likely to change
            priority: 0.8, // Priority of this URL relative to other URLs on your site
            // Additional sitemap fields can be added here
            images: [], // Image sitemap data if needed
            videos: [], // Video sitemap data if needed
            news: null, // News sitemap data if needed
          }
        });
      console.log('sitemapData length: ', sitemapData.length);

      // Update build record with URL count
      await strapi.entityService.update('plugin::strapi-plugin-custom-sitemap.sitemap-build', sitemapBuild.id, {
        data: {
          totalUrls: sitemapData.length,
          sitemapUrls: sitemapData,
          locale
        } as any, // Workaround: 'as any' due to Strapi plugin type limitations
      });

      let response: any = null;
      try {
        // Call external API to rebuild sitemap
        response = await webAxiosinstance.post('/replace-sitemap', {
          [locale]: sitemapData.map(({ loc }) => loc),
        });
        console.log('/replace-sitemap response: ', response);
      } catch (error) {
        console.error(error)
        response = error.response;
      }

      const buildEndTime = new Date();
      const buildDuration = buildEndTime.getTime() - buildStartTime.getTime();

      if (!response?.data?.success) {
        // Update build record as failed
        await strapi.entityService.update('plugin::strapi-plugin-custom-sitemap.sitemap-build', sitemapBuild.id, {
          data: {
            status: 'failed',
            buildEndTime: buildEndTime.toISOString(),
            buildDuration,
            errorLog: `External API responded with status: ${response?.data?.message}`,
            failedUrls: sitemapData.length,
          } as any, // Workaround: 'as any' due to Strapi plugin type limitations
        });
        throw new Error(`External API responded with status: ${response?.data?.message}`);
      }

      // Update build record as completed
      await strapi.entityService.update('plugin::strapi-plugin-custom-sitemap.sitemap-build', sitemapBuild.id, {
        data: {
          status: 'completed',
          buildEndTime: buildEndTime.toISOString(),
          buildDuration,
          processedUrls: sitemapData.length,
          lastModified: buildEndTime.toISOString(),
          buildLog: `Successfully processed ${sitemapData.length} URLs`,
        } as any, // Workaround: 'as any' due to Strapi plugin type limitations
      });

      return {
        success: true,
        rebuildDate: buildEndTime.toISOString(),
        itemsProcessed: sitemapData.length,
        message: 'Sitemap rebuilt successfully',
        buildId: sitemapBuild.id,
        buildDuration,
        locale
      };
    } catch (error) {
      strapi.log.error('Error rebuilding sitemap:', error);

      // Update build record as failed if it exists
      if (sitemapBuild) {
        const buildEndTime = new Date();
        const buildDuration = buildEndTime.getTime() - buildStartTime.getTime();

        await strapi.entityService.update('plugin::strapi-plugin-custom-sitemap.sitemap-build', sitemapBuild.id, {
          data: {
            status: 'failed',
            buildEndTime: buildEndTime.toISOString(),
            buildDuration,
            errorLog: error instanceof Error ? error.message : 'Unknown error occurred',
            locale
          } as any, // Workaround: 'as any' due to Strapi plugin type limitations
        });
      }

      throw error;
    }
  },

  async getStaticContentStats(locale: string = 'en') {
    try {
      if (!strapi.entityService) {
        throw new Error('Entity service not available');
      }

      const totalItems = await strapi.entityService.count('api::static-content.static-content', {
        locale
      } as any);
      const includedItems = await strapi.entityService.count('api::static-content.static-content', {
        filters: {
          includeInSitemap: true,
        },
        locale,
      } as any);
      const publishedIncludedItems = await strapi.entityService.count('api::static-content.static-content', {
        filters: {
          includeInSitemap: true,
          publishedAt: {
            $notNull: true,
          },
        },
        locale,
      } as any);

      return {
        totalItems,
        includedItems,
        publishedIncludedItems,
        excludedItems: totalItems - includedItems,
      };
    } catch (error) {
      strapi.log.error('Error fetching static content stats:', error);
      throw error;
    }
  },

  async getSitemapBuilds(limit = 10, locale: string = 'en') {
    try {
      if (!strapi.entityService) {
        throw new Error('Entity service not available');
      }

      const builds = await strapi.entityService.findMany('plugin::strapi-plugin-custom-sitemap.sitemap-build', {
        sort: { createdAt: 'desc' },
        limit,
        filters: { locale, }
      });

      return builds || [];
    } catch (error) {
      strapi.log.error('Error fetching sitemap builds:', error);
      throw error;
    }
  },

  async getSitemapBuildById(id: string) {
    try {
      if (!strapi.entityService) {
        throw new Error('Entity service not available');
      }

      const build = await strapi.entityService.findOne('plugin::strapi-plugin-custom-sitemap.sitemap-build', id);
      return build;
    } catch (error) {
      strapi.log.error('Error fetching sitemap build:', error);
      throw error;
    }
  },
});
