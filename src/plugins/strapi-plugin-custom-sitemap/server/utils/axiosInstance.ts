/**
 * axios with a custom config for web page.
 */
import axios from 'axios'

export const WEBSITE_API_URL = process.env.WEBSITE_API_URL || 'https://homepagedev.sendvalu.com/api'; // Replace with actual URL
const WEBSITE_API_PASS_KEY = process.env.WEBSITE_API_PASS_KEY || 'isNotBearer-weAreBear';

export const webAxiosinstance = axios.create({
  baseURL: WEBSITE_API_URL,
  headers: {
    'team-pass-key': WEBSITE_API_PASS_KEY,
    Accept: 'application/json',
    'Content-Type': 'application/json',
  }
})

