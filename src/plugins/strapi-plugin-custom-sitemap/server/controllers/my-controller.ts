import { Strapi } from '@strapi/strapi';

export default ({ strapi }: { strapi: Strapi }) => ({

  async getSitemapData(ctx) {
    try {
      const locale = ctx.query.locale || 'en';
      const data = await strapi
        .plugin('strapi-plugin-custom-sitemap')
        .service('myService')
        .getSitemapData(locale);

      ctx.body = { data };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      ctx.throw(500, `Failed to fetch sitemap data: ${errorMessage}`);
    }
  },

  async rebuildSitemap(ctx) {
    try {
      const locale = ctx.query.locale || 'en';
      const triggeredBy = ctx.state.user?.email || 'admin-dashboard';

      const result = await strapi
        .plugin('strapi-plugin-custom-sitemap')
        .service('myService')
        .rebuildSitemap({ triggeredBy, locale });

      ctx.body = { data: result };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      ctx.throw(500, `Failed to rebuild sitemap: ${errorMessage}`);
    }
  },

  async getStats(ctx) {
    try {
      const locale = ctx.query.locale || 'en';
      const stats = await strapi
        .plugin('strapi-plugin-custom-sitemap')
        .service('myService')
        .getStaticContentStats(locale);

      ctx.body = { data: stats };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      ctx.throw(500, `Failed to fetch stats: ${errorMessage}`);
    }
  },

  async getSitemapBuilds(ctx) {
    try {
      const { limit } = ctx.query;
      const locale = ctx.query.locale || 'en';
      const builds = await strapi
        .plugin('strapi-plugin-custom-sitemap')
        .service('myService')
        .getSitemapBuilds(limit ? parseInt(limit as string, 10) : 10, locale);

      ctx.body = { data: builds };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      ctx.throw(500, `Failed to fetch sitemap builds: ${errorMessage}`);
    }
  },

  async getSitemapBuild(ctx) {
    try {
      const { id } = ctx.params;
      const build = await strapi
        .plugin('strapi-plugin-custom-sitemap')
        .service('myService')
        .getSitemapBuildById(id);

      if (!build) {
        return ctx.throw(404, 'Sitemap build not found');
      }

      ctx.body = { data: build };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      ctx.throw(500, `Failed to fetch sitemap build: ${errorMessage}`);
    }
  },
});
