
export default [
  {
    method: 'GET',
    path: '/sitemap-data',
    handler: 'myController.getSitemapData',
    config: {
      policies: [],
    },
  },
  {
    method: 'POST',
    path: '/rebuild-sitemap',
    handler: 'myController.rebuildSitemap',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/stats',
    handler: 'myController.getStats',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/builds',
    handler: 'myController.getSitemapBuilds',
    config: {
      policies: [],
    },
  },
  {
    method: 'GET',
    path: '/builds/:id',
    handler: 'myController.getSitemapBuild',
    config: {
      policies: [],
    },
  },
]