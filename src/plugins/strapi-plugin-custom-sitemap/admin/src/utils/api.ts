import { request } from '@strapi/helper-plugin';
import { pluginName } from '../pluginId';


// For Strapi v4 admin plugin routes, the pattern is /{plugin-name}/{route}
// The @strapi/helper-plugin request function automatically handles the /admin/plugins prefix
export const sitemapApi = {
  getSitemapData: (locale: string = 'en') => request(`/${pluginName}/sitemap-data?locale=${locale}`, { method: 'GET' }),
  getStats: (locale: string = 'en') => request(`/${pluginName}/stats?locale=${locale}`, { method: 'GET' }),
  rebuildSitemap: (locale: string = 'en') =>
    request(`/${pluginName}/rebuild-sitemap?locale=${locale}`, {
      method: 'POST',
      body: {},
    }),
  getSitemapBuilds: (limit?: number, locale: string = 'en') =>
    request(`/${pluginName}/builds?locale=${locale}${limit ? `&limit=${limit}` : ''}`, { method: 'GET' }),
  getSitemapBuild: (id: string) =>
    request(`/${pluginName}/builds/${id}`, { method: 'GET' }),
};

export default sitemapApi;