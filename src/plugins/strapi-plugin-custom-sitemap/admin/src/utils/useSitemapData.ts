import { useState, useEffect, useCallback } from 'react';
import { useNotification } from '@strapi/helper-plugin';
import sitemapApi from './api';
import { SitemapData, Stats, SitemapBuild } from '../types';

export const useSitemapData = ({ locale = 'en' }: { locale: string }) => {
  const [sitemapData, setSitemapData] = useState<SitemapData | null>(null);
  const [stats, setStats] = useState<Stats | null>(null);
  const [sitemapBuilds, setSitemapBuilds] = useState<SitemapBuild[]>([]);
  const [loading, setLoading] = useState(true);
  const [rebuilding, setRebuilding] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const toggleNotification = useNotification();

  const fetchData = useCallback(async (lang: string = locale) => {
    try {
      console.log({ locale, lang });
      setLoading(true);
      setError(null);

      const [sitemapResponse, statsResponse, buildsResponse] = await Promise.all([
        sitemapApi.getSitemapData(lang),
        sitemapApi.getStats(lang),
        sitemapApi.getSitemapBuilds(10, lang),
      ]);

      setSitemapData(sitemapResponse.data);
      setStats(statsResponse.data);
      setSitemapBuilds(buildsResponse.data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch sitemap data';
      setError(errorMessage);
      console.error('Error fetching data:', err);
      toggleNotification({
        type: 'warning',
        message: errorMessage,
      });
    } finally {
      setLoading(false);
    }
  }, [toggleNotification, locale]);

  const rebuildSitemap = useCallback(async (lang: string = locale) => {
    try {
      setRebuilding(true);
      setError(null);

      const response = await sitemapApi.rebuildSitemap(lang);

      toggleNotification({
        type: 'success',
        message: `Sitemap rebuilt successfully! Processed ${response.data.itemsProcessed} items in ${Math.round(response.data.buildDuration / 1000)}s.`,
      });

      // Refresh data after successful rebuild
      await fetchData(lang);

      return { success: true, data: response.data };
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to rebuild sitemap';
      setError(errorMessage);
      console.error('Error rebuilding sitemap:', err);
      toggleNotification({
        type: 'danger',
        message: 'Failed to rebuild sitemap. Please check the API endpoint and try again.',
      });
      return { success: false, error: errorMessage };
    } finally {
      setRebuilding(false);
    }
  }, [fetchData, toggleNotification, locale]);

  const fetchSitemapBuild = useCallback(async (id: string) => {
    try {
      const response = await sitemapApi.getSitemapBuild(id);
      return response.data;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch sitemap build';
      console.error('Error fetching sitemap build:', err);
      toggleNotification({
        type: 'warning',
        message: errorMessage,
      });
      return null;
    }
  }, [toggleNotification]);

  useEffect(() => {
    console.log({ locale })
    fetchData(locale);
  }, [fetchData, locale]);

  return {
    sitemapData,
    stats,
    sitemapBuilds,
    loading,
    rebuilding,
    error,
    fetchData,
    rebuildSitemap,
    fetchSitemapBuild,
  };
};