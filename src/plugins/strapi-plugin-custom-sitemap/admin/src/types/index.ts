export interface SitemapData {
  lastRebuildDate: string | null;
  staticContentItems: StaticContentItem[];
  totalItems: number;
  latestBuild: SitemapBuild | null;
}

export interface Stats {
  totalItems: number;
  includedItems: number;
  publishedIncludedItems: number;
  excludedItems: number;
}

export interface StaticContentItem {
  id: number;
  referenceName?: string;
  url: string;
  country?: string;
  createdAt: string;
  updatedAt: string;
  publishedAt?: string;
  includeInSitemap: boolean;
  identifier?: string;
  referenceId: number;
  model: string;
  fieldsAndValues?: Record<string, any>;
  lastOriginalUpdateDate?: string;
}

export interface SitemapBuild {
  id: number;
  buildId: string;
  buildName: string;
  status: 'pending' | 'in-progress' | 'completed' | 'failed';
  buildType: 'manual' | 'automatic' | 'scheduled';
  externalApiEndpoint?: string;
  totalUrls: number;
  processedUrls: number;
  failedUrls: number;
  buildStartTime?: string;
  buildEndTime?: string;
  buildDuration?: number;
  sitemapUrls?: SitemapUrl[];
  errorLog?: string;
  buildLog?: string;
  sitemapSize?: number;
  compressionEnabled: boolean;
  sitemapFormat: 'xml' | 'txt' | 'rss';
  lastModified?: string;
  changeFrequency: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority: number;
  includeImages: boolean;
  includeVideos: boolean;
  includeNews: boolean;
  locales?: string[];
  robotsTxtUpdated: boolean;
  searchEnginesNotified?: string[];
  buildMetadata?: Record<string, any>;
  triggeredBy?: string;
  buildVersion?: string;
  createdAt: string;
  updatedAt: string;
}

export interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: string;
  priority?: number;
  images?: any[];
  videos?: any[];
  news?: any;
}

export interface RebuildSitemapResponse {
  success: boolean;
  rebuildDate: string;
  itemsProcessed: number;
  message: string;
  buildId: number;
  buildDuration: number;
}

export interface ApiResponse<T> {
  data: T;
}

export interface RebuildSitemapRequest {
  externalApiEndpoint: string;
}