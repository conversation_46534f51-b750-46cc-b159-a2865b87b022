/*
 *
 * HomePage
 *
 */
import {
  Layout,
  ContentLayout,
  HeaderLayout,
  Main,
  Box,
  Typography,
  Button,
  Stack,
  Loader,
  Flex,
} from '@strapi/design-system';
import { Refresh } from '@strapi/icons';
import React from 'react';
import { useSitemapData } from '../../utils/useSitemapData';
import {
  StatsCards,
  RebuildSitemapCard,
  StaticContentTable,
  SitemapBuildsTable,
} from '../../components';
import LocaleDropdown from '../../components/LocaleDropdown';

const HomePage = () => {
  const [selectedLocale, setSelectedLocale] = React.useState('en');
  const {
    sitemapData,
    stats,
    sitemapBuilds,
    loading,
    rebuilding,
    error,
    fetchData,
    rebuildSitemap,
  } = useSitemapData({ locale: selectedLocale });

  const handleLocaleChange = (locale: string) => {
    setSelectedLocale(locale);
  };

  if (loading) {
    return (
      <Layout>
        <Main>
          <HeaderLayout
            title="Custom Sitemap Dashboard"
            subtitle="Manage your website sitemap"
          />
          <ContentLayout>
            <Box padding={8} style={{ textAlign: 'center' }}>
              <Loader />
              <Typography variant="omega" style={{ marginTop: '16px' }}>
                Loading dashboard data...
              </Typography>
            </Box>
          </ContentLayout>
        </Main>
      </Layout>
    );
  }

  if (error) {
    return (
      <Layout>
        <Main>
          <HeaderLayout
            title="Custom Sitemap Dashboard"
            subtitle="Manage your website sitemap"
          />
          <ContentLayout>
            <Box padding={8} style={{ textAlign: 'center' }}>
              <Typography variant="omega" textColor="danger600">
                Error loading dashboard: {error}
              </Typography>
              <Button onClick={() => fetchData()} style={{ marginTop: '16px' }}>
                Try Again
              </Button>
            </Box>
          </ContentLayout>
        </Main>
      </Layout>
    );
  }

  return (
    <Layout>
      <Main>
        <HeaderLayout
          title="Custom Sitemap Dashboard"
          subtitle="Manage your website sitemap and monitor static content"
          primaryAction={
            <Flex gap={3} alignItems="center">
              <Button
                onClick={() => fetchData()}
                startIcon={<Refresh />}
                size="L"
                variant="secondary"
              >
                Refresh Data
              </Button>
              <LocaleDropdown value={selectedLocale} onChange={handleLocaleChange} />
            </Flex>
          }
        />
        <ContentLayout>
          <Stack spacing={6}>
            {/* Stats Cards */}
            <StatsCards
              stats={stats}
              lastRebuildDate={sitemapData?.latestBuild?.createdAt || sitemapData?.lastRebuildDate || null}
            />

            {/* Rebuild Sitemap Section */}
            <RebuildSitemapCard
              onRebuild={rebuildSitemap}
              rebuilding={rebuilding}
            />

            {/* Sitemap Builds History */}
            <SitemapBuildsTable builds={sitemapBuilds} />

            {/* Recent Static Content */}
            <StaticContentTable
              items={sitemapData?.staticContentItems || []}
              lastRebuildDate={sitemapData?.latestBuild?.createdAt || sitemapData?.lastRebuildDate || null}
            />
          </Stack>
        </ContentLayout>
      </Main>
    </Layout>
  );
};

export default HomePage;
