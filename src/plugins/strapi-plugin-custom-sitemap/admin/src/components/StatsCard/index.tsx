import React from 'react';
import { <PERSON>, Card<PERSON>eader, CardBody, CardTitle } from '@strapi/design-system/Card';
import { Flex } from '@strapi/design-system/Flex';
import { Typography } from '@strapi/design-system/Typography';
import { Icon } from '@strapi/design-system/Icon';
import { BarChart } from '@strapi/icons';

interface StatsCardProps {
  icon?: React.ComponentType<any>;
  title: string;
  value: any;
  description?: string;
}

const StatsCard: React.FC<StatsCardProps> = ({
  icon: IconComponent = BarChart,
  title,
  value,
  description = 'Static content items',
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle>
          <Flex alignItems="center" gap={2}>
            <Icon as={IconComponent} />
            {title}
          </Flex>
        </CardTitle>
      </CardHeader>
      <CardBody>
        <Typography variant="alpha">{value}</Typography>
        <Typography variant="pi" textColor="neutral600">
          {description}
        </Typography>
      </CardBody>
    </Card>
  );
};

export default StatsCard;
