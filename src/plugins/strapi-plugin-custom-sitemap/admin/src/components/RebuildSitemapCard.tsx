import React, { useState } from 'react';
import {
  Card,
  CardBody,
  CardHeader,
  CardTitle,
  Typography,
  Button,
  TextInput,
  Stack,
  Flex,
  Alert,
} from '@strapi/design-system';
import { Refresh } from '@strapi/icons';

interface RebuildSitemapCardProps {
  onRebuild: () => Promise<{ success: boolean; error?: string }>;
  rebuilding: boolean;
}

const RebuildSitemapCard: React.FC<RebuildSitemapCardProps> = ({ onRebuild, rebuilding }) => {
  const [lastRebuildStatus, setLastRebuildStatus] = useState<'success' | 'error' | null>(null);

  const handleRebuildSitemap = async () => {
    const result = await onRebuild();
    setLastRebuildStatus(result.success ? 'success' : 'error');
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Rebuild Sitemap</CardTitle>
      </CardHeader>
      <CardBody>
        <Stack spacing={4}>
          <Typography variant="omega" textColor="neutral600">
            Rebuild the sitemap with current static content.
          </Typography>
          
          {/* <TextInput
            disabled
            label="External API Endpoint"
            placeholder="https://your-website.com/api/rebuild-sitemap"
            value={externalApiEndpoint}
            onChange={(e) => setExternalApiEndpoint(e.target.value)}
            hint="The API endpoint that will receive the sitemap data"
          /> */}
          
          <Flex justifyContent="flex-start">
            <Button
              onClick={handleRebuildSitemap}
              loading={rebuilding}
              disabled={rebuilding}
              startIcon={<Refresh />}
              size="L"
            >
              {rebuilding ? 'Rebuilding...' : 'Rebuild Sitemap'}
            </Button>
          </Flex>

          {lastRebuildStatus && (
            <Alert
              variant={lastRebuildStatus === 'success' ? 'success' : 'danger'}
              onClose={() => setLastRebuildStatus(null)}
              title={lastRebuildStatus === 'success' ? 'Success' : 'Error'}
            >
              {lastRebuildStatus === 'success'
                ? 'Sitemap has been successfully rebuilt and sent to your external API.'
                : 'Failed to rebuild sitemap. Please check your API endpoint and try again.'}
            </Alert>
          )}
        </Stack>
      </CardBody>
    </Card>
  );
};

export default RebuildSitemapCard;