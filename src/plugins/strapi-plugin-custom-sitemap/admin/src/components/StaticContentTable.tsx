import React from 'react';
import {
  <PERSON>,
  Card<PERSON><PERSON>,
  Card<PERSON><PERSON>er,
  Card<PERSON><PERSON><PERSON>,
  Typography,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Flex,
  Icon,
  Box,
} from '@strapi/design-system';
import { Link } from '@strapi/icons';
import { StaticContentItem } from '../types';

interface StaticContentTableProps {
  items: StaticContentItem[];
  lastRebuildDate: string | null;
}

const StaticContentTable: React.FC<StaticContentTableProps> = ({ items, lastRebuildDate }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          Recent Static Content 
          {lastRebuildDate && (
            <Typography variant="pi" textColor="neutral600" style={{ marginLeft: '8px' }}>
              (Created after last rebuild)
            </Typography>
          )}
        </CardTitle>
      </CardHeader>
      <CardBody>
        {items && items.length > 0 ? (
          <Table colCount={5} rowCount={items.length + 1}>
            <Thead>
              <Tr>
                <Th>
                  <Typography variant="sigma">Reference Name</Typography>
                </Th>
                <Th>
                  <Typography variant="sigma">URL</Typography>
                </Th>
                <Th>
                  <Typography variant="sigma">Country</Typography>
                </Th>
                <Th>
                  <Typography variant="sigma">Created</Typography>
                </Th>
                <Th>
                  <Typography variant="sigma">Status</Typography>
                </Th>
              </Tr>
            </Thead>
            <Tbody>
              {items.map((item, index) => (
                <Tr key={item.id || index}>
                  <Td>
                    <Typography variant="omega">
                      {item.referenceName || 'N/A'}
                    </Typography>
                  </Td>
                  <Td>
                    <Flex alignItems="center" gap={2}>
                      <Icon as={Link} />
                      <Typography 
                        variant="omega" 
                        style={{ 
                          maxWidth: '200px', 
                          overflow: 'hidden', 
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap'
                        }}
                        title={item.url}
                      >
                        {item.url}
                      </Typography>
                    </Flex>
                  </Td>
                  <Td>
                    <Typography variant="omega">
                      {item.country || 'N/A'}
                    </Typography>
                  </Td>
                  <Td>
                    <Typography variant="omega">
                      {formatDate(item.createdAt)}
                    </Typography>
                  </Td>
                  <Td>
                    <Badge variant={item.publishedAt ? 'success' : 'secondary'}>
                      {item.publishedAt ? 'Published' : 'Draft'}
                    </Badge>
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        ) : (
          <Box padding={4} style={{ textAlign: 'center' }}>
            <Typography variant="omega" textColor="neutral600">
              {lastRebuildDate 
                ? 'No new static content items created since last rebuild.'
                : 'No static content items found with includeInSitemap enabled.'}
            </Typography>
          </Box>
        )}
      </CardBody>
    </Card>
  );
};

export default StaticContentTable;