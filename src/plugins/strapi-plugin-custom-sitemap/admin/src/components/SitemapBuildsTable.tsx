import React from 'react';
import {
  <PERSON>,
  Card<PERSON>ody,
  CardHeader,
  Card<PERSON><PERSON>le,
  <PERSON>po<PERSON>,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Badge,
  Flex,
  Icon,
  Box,
} from '@strapi/design-system';
import { Clock, CheckCircle, CrossCircle, Loader } from '@strapi/icons';
import { SitemapBuild } from '../types';

interface SitemapBuildsTableProps {
  builds: SitemapBuild[];
}

const SitemapBuildsTable: React.FC<SitemapBuildsTableProps> = ({ builds }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const formatDuration = (duration?: number) => {
    if (!duration) return 'N/A';
    const seconds = Math.round(duration / 1000);
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="success">Completed</Badge>;
      case 'failed':
        return <Badge variant="danger">Failed</Badge>;
      case 'in-progress':
        return <Badge variant="secondary">In Progress</Badge>;
      case 'pending':
        return <Badge variant="secondary">Pending</Badge>;
      default:
        return <Badge variant="neutral">{status}</Badge>;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <Icon as={CheckCircle} color="success600" />;
      case 'failed':
        return <Icon as={CrossCircle} color="danger600" />;
      case 'in-progress':
        return <Icon as={Loader} color="primary600" />;
      case 'pending':
        return <Icon as={Clock} color="neutral600" />;
      default:
        return <Icon as={Clock} color="neutral600" />;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Recent Sitemap Builds</CardTitle>
      </CardHeader>
      <CardBody>
        {builds && builds.length > 0 ? (
          <Table colCount={7} rowCount={builds.length + 1}>
            <Thead>
              <Tr>
                <Th>
                  <Typography variant="sigma">Status</Typography>
                </Th>
                <Th>
                  <Typography variant="sigma">Build Name</Typography>
                </Th>
                <Th>
                  <Typography variant="sigma">URLs</Typography>
                </Th>
                <Th>
                  <Typography variant="sigma">Duration</Typography>
                </Th>
                <Th>
                  <Typography variant="sigma">Type</Typography>
                </Th>
                <Th>
                  <Typography variant="sigma">Triggered By</Typography>
                </Th>
                <Th>
                  <Typography variant="sigma">Created</Typography>
                </Th>
              </Tr>
            </Thead>
            <Tbody>
              {builds.map((build) => (
                <Tr key={build.id}>
                  <Td>
                    <Flex alignItems="center" gap={2}>
                      {getStatusIcon(build.status)}
                      {getStatusBadge(build.status)}
                    </Flex>
                  </Td>
                  <Td>
                    <Typography variant="omega" fontWeight="bold">
                      {build.buildName}
                    </Typography>
                    {build.buildId && (
                      <Typography variant="pi" textColor="neutral600">
                        ID: {build.buildId}
                      </Typography>
                    )}
                  </Td>
                  <Td>
                    <Typography variant="omega">
                      {build.processedUrls > 0 ? (
                        <>
                          <span style={{ color: 'green' }}>{build.processedUrls}</span>
                          {build.failedUrls > 0 && (
                            <>
                              {' / '}
                              <span style={{ color: 'red' }}>{build.failedUrls} failed</span>
                            </>
                          )}
                          {' / '}
                          {build.totalUrls} total
                        </>
                      ) : (
                        `${build.totalUrls} total`
                      )}
                    </Typography>
                  </Td>
                  <Td>
                    <Typography variant="omega">
                      {formatDuration(build.buildDuration)}
                    </Typography>
                  </Td>
                  <Td>
                    <Badge variant="secondary">
                      {build.buildType.charAt(0).toUpperCase() + build.buildType.slice(1)}
                    </Badge>
                  </Td>
                  <Td>
                    <Typography variant="omega">
                      {build.triggeredBy || 'System'}
                    </Typography>
                  </Td>
                  <Td>
                    <Typography variant="omega">
                      {formatDate(build.createdAt)}
                    </Typography>
                  </Td>
                </Tr>
              ))}
            </Tbody>
          </Table>
        ) : (
          <Box padding={4} style={{ textAlign: 'center' }}>
            <Typography variant="omega" textColor="neutral600">
              No sitemap builds found. Create your first build by rebuilding the sitemap above.
            </Typography>
          </Box>
        )}
      </CardBody>
    </Card>
  );
};

export default SitemapBuildsTable;