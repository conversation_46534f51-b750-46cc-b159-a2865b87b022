import React, { useEffect, useState } from 'react';
import { Select, Option } from '@strapi/design-system/Select';
import { request } from '@strapi/helper-plugin';

interface Locale {
  id: number | string;
  code: string;
  name: string;
}

interface LocaleDropdownProps {
  value: string;
  onChange: (locale: string) => void;
}

const LocaleDropdown: React.FC<LocaleDropdownProps> = ({ value, onChange }) => {
  const [locales, setLocales] = useState<Locale[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    request('/i18n/locales', { method: 'GET' })
      .then((data: Locale[]) => {
        setLocales(data);
        setLoading(false);
      })
      .catch(() => setLoading(false));
  }, []);

  return (
    <Select
      value={value}
      onChange={onChange}
      disabled={loading}
      size="L"
      placeholder="Select locale"
      style={{ minWidth: 180 }}
    >
      {locales.map((locale) => (
        <Option key={locale.code} value={locale.code}>
          {locale.name}
        </Option>
      ))}
    </Select>
  );
};

export default LocaleDropdown; 