import React from 'react';
import {
  Grid,
  GridItem,
  Card,
  CardBody,
  CardHeader,
  CardTitle,
  Typography,
  Flex,
  Icon,
  Badge,
} from '@strapi/design-system';
import { Check, Cross, Calendar, BarChart } from '@strapi/icons';
import { Stats } from '../../types';
import StatsCard from '../StatsCard';

interface StatsCardsProps {
  stats: Stats | null;
  lastRebuildDate: string | null;
}

const StatsCards: React.FC<StatsCardsProps> = ({ stats, lastRebuildDate }) => {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <Grid gap={4}>
      <GridItem col={3}>
        <StatsCard
          title="Total Content"
          value={stats?.totalItems || 0}
          description="Static content items"
          icon={BarChart}
        />
      </GridItem>

      <GridItem col={3}>
        <StatsCard
          title="Included in Sitemap"
          value={stats?.publishedIncludedItems || 0}
          description="Published & included"
          icon={Check}
        />
      </GridItem>

      <GridItem col={3}>
        <StatsCard
          title="Excluded"
          value={stats?.excludedItems || 0}
          description="Not included in sitemap"
          icon={Cross}
        />
      </GridItem>

      <GridItem col={3}>
        <StatsCard
          title="Last Rebuild"
          value={lastRebuildDate ? formatDate(lastRebuildDate) : 'Never rebuilt'}
          description={lastRebuildDate ? 'Completed' : 'Pending'}
          icon={Calendar}
        />
      </GridItem>
    </Grid>
  );
};

export default StatsCards;