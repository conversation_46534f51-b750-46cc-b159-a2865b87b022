{"plugin.name": "Custom Sitemap", "plugin.description": "Manage your website sitemap and monitor static content", "dashboard.title": "Custom Sitemap Dashboard", "dashboard.subtitle": "Manage your website sitemap and monitor static content", "stats.totalContent": "Total Content", "stats.includedInSitemap": "Included in Sitemap", "stats.excluded": "Excluded", "stats.lastRebuild": "Last Rebuild", "rebuild.title": "Rebuild Sitemap", "rebuild.description": "Rebuild the sitemap with current static content.", "rebuild.endpoint.label": "External API Endpoint", "rebuild.endpoint.placeholder": "https://your-website.com/api/rebuild-sitemap", "rebuild.endpoint.hint": "The API endpoint that will receive the sitemap data", "rebuild.button": "Rebuild Sitemap", "rebuild.button.loading": "Rebuilding...", "rebuild.success": "Sitemap has been successfully rebuilt and sent to your external API.", "rebuild.error": "Failed to rebuild sitemap. Please check your API endpoint and try again.", "content.recent": "Recent Static Content", "content.afterRebuild": "(Created after last rebuild)", "content.noItems": "No static content items found with includeInSitemap enabled.", "content.noNewItems": "No new static content items created since last rebuild.", "table.referenceName": "Reference Name", "table.url": "URL", "table.country": "Country", "table.created": "Created", "table.status": "Status", "status.published": "Published", "status.draft": "Draft", "status.completed": "Completed", "status.pending": "Pending", "button.refresh": "Refresh Data", "loading.message": "Loading dashboard data...", "notification.dataFetchError": "Failed to fetch sitemap data", "notification.endpointRequired": "Please enter an external API endpoint", "notification.rebuildSuccess": "Sitemap rebuilt successfully! Processed {count} items.", "notification.rebuildError": "Failed to rebuild sitemap. Please check the API endpoint and try again."}