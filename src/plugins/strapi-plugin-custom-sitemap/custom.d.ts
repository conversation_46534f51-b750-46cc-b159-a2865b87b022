declare module '@strapi/design-system/*';
declare module '@strapi/design-system';
declare module '@strapi/icons';
declare module '@strapi/icons/*';
declare module '@strapi/helper-plugin';

// Custom type definitions for our plugin
declare global {
  namespace Strapi {
    interface Schemas {
      'plugin::strapi-plugin-custom-sitemap.sitemap-build': {
        kind: 'collectionType';
        attributes: {
          buildId: string;
          buildName: string;
          status: 'pending' | 'in-progress' | 'completed' | 'failed';
          buildType: 'manual' | 'automatic' | 'scheduled';
          externalApiEndpoint?: string;
          totalUrls: number;
          processedUrls: number;
          failedUrls: number;
          buildStartTime?: string;
          buildEndTime?: string;
          buildDuration?: number;
          sitemapUrls?: any[];
          errorLog?: string;
          buildLog?: string;
          sitemapSize?: number;
          compressionEnabled: boolean;
          sitemapFormat: 'xml' | 'txt' | 'rss';
          lastModified?: string;
          changeFrequency: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
          priority: number;
          includeImages: boolean;
          includeVideos: boolean;
          includeNews: boolean;
          locales?: string[];
          robotsTxtUpdated: boolean;
          searchEnginesNotified?: string[];
          buildMetadata?: Record<string, any>;
          triggeredBy?: string;
          buildVersion?: string;
          createdAt: string;
          updatedAt: string;
        };
      };
    }
  }
}
