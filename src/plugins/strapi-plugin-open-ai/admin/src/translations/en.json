{"Plugin.name": "Open AI Completion", "RightLinks.button": "Completion", "Modal.tabs.prompt": "Prompt", "Modal.tabs.settings": "Settings", "Modal.tabs.prompt.generate.button.text.default": "Generate", "Modal.tabs.prompt.generate.button.text.pending": "Generating...", "Modal.tabs.settings.download.button.text.default": "Download models", "Modal.tabs.settings.download.button.text.pending": "Downloading...", "Modal.tabs.prompt.finish-reason.text": "Finish reason:", "Modal.tabs.settings.temperature.hint.text": "Between 0 and 1 (default). Higher values means the model will take more risks. Try 0,9 for more creative applications, and 0 for ones with a well-defined answer.", "Modal.tabs.settings.maxTokens.hint.text": "The token count of your prompt plus max_tokens cannot exceed the model's context length. Most models have a context length of 2048 tokens (except for the newest models, which support 4096).", "Modal.cancel.button.text": "Cancel", "Modal.save-model.button.text.default": "Save model settings", "Modal.save-model.button.text.after": "Saved!", "Modal.clear.button.text": "Clear completion", "Modal.copy-to-clipboard.button.text": "Copy to clipboard", "Modal.tabs.prompt.placeholder": "Explain what is Strapi to a 5 years old"}