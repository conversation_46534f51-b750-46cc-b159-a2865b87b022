"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = ({ strapi }) => ({
    async find(params) {
        var _a;
        return await ((_a = strapi.entityService) === null || _a === void 0 ? void 0 : _a.findMany('plugin::strapi-custom-images-plugin.image-context-localized', {
            ...params,
            populate: ['image', 'localizations'],
        }));
    },
    async findOne(id, params) {
        var _a;
        return await ((_a = strapi.entityService) === null || _a === void 0 ? void 0 : _a.findOne('plugin::strapi-custom-images-plugin.image-context-localized', id, {
            ...params,
            populate: ['image', 'localizations'],
        }));
    },
    async create(data) {
        var _a;
        return await ((_a = strapi.entityService) === null || _a === void 0 ? void 0 : _a.create('plugin::strapi-custom-images-plugin.image-context-localized', {
            data,
            populate: ['image'],
        }));
    },
    async update(id, data) {
        var _a;
        return await ((_a = strapi.entityService) === null || _a === void 0 ? void 0 : _a.update('plugin::strapi-custom-images-plugin.image-context-localized', id, {
            data,
            populate: ['image'],
        }));
    },
    async delete(id) {
        var _a;
        return await ((_a = strapi.entityService) === null || _a === void 0 ? void 0 : _a.delete('plugin::strapi-custom-images-plugin.image-context-localized', id));
    },
    async getLocalizedDataByImageId({ imageId, locale, pageId, }) {
        var _a, _b;
        locale = locale || 'en';
        const customImagesFound = await ((_a = strapi.entityService) === null || _a === void 0 ? void 0 : _a.findMany('plugin::strapi-custom-images-plugin.image-context-localized', {
            filters: {
                image: {
                    id: imageId
                }
            },
            locale: locale,
            populate: ['image', 'captionByPage', 'captionByPage.page'],
        }));
        const customImageFound = customImagesFound === null || customImagesFound === void 0 ? void 0 : customImagesFound[0];
        console.log('getLocalizedDataByImageId customImageFound: ', JSON.stringify(customImageFound, null, 2));
        if (!customImageFound) {
            return null;
        }
        if (pageId) {
            const imageByPageFound = (_b = customImageFound.captionByPage) === null || _b === void 0 ? void 0 : _b.find((c) => { var _a; return ((_a = c.page) === null || _a === void 0 ? void 0 : _a.id) === pageId; });
            if (imageByPageFound) {
                return {
                    name: customImageFound.name,
                    image: customImageFound.image,
                    alt: imageByPageFound.alt,
                    caption: imageByPageFound.caption,
                };
            }
        }
        return {
            name: customImageFound.name,
            image: customImageFound.image,
            alt: customImageFound.alt,
            caption: customImageFound.caption,
        };
    }
});
