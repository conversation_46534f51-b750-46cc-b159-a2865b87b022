"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = ({ strapi }) => ({
    async find(ctx) {
        var _a, _b;
        try {
            // Get pagination parameters from query
            const { page = 1, pageSize = 10, sort = 'createdAt:desc' } = ctx.query;
            // Parse pagination parameters
            const pageNumber = parseInt(page, 10);
            const pageSizeNumber = parseInt(pageSize, 10);
            // Calculate start for pagination
            const start = (pageNumber - 1) * pageSizeNumber;
            // Get total count
            const total = await ((_a = strapi.entityService) === null || _a === void 0 ? void 0 : _a.count('plugin::strapi-custom-images-plugin.image-context-localized', {})) || 0;
            // Calculate page count
            const pageCount = Math.ceil(total / pageSizeNumber);
            // Get image context localizeds with pagination
            const results = await ((_b = strapi.entityService) === null || _b === void 0 ? void 0 : _b.findMany('plugin::strapi-custom-images-plugin.image-context-localized', {
                start,
                limit: pageSizeNumber,
                sort: [sort],
                populate: ['image'],
            }));
            // Return paginated results
            return {
                results,
                pagination: {
                    page: pageNumber,
                    pageSize: pageSizeNumber,
                    pageCount,
                    total,
                },
            };
        }
        catch (error) {
            ctx.throw(500, error);
        }
    },
    async findOne(ctx) {
        var _a;
        try {
            const { id } = ctx.params;
            const entity = await ((_a = strapi.entityService) === null || _a === void 0 ? void 0 : _a.findOne('plugin::strapi-custom-images-plugin.image-context-localized', id, {
                populate: ['image'],
            }));
            if (!entity) {
                return ctx.notFound('Image context localized not found');
            }
            return entity;
        }
        catch (error) {
            ctx.throw(500, error);
        }
    },
    async create(ctx) {
        var _a;
        try {
            const { body } = ctx.request;
            const entity = await ((_a = strapi.entityService) === null || _a === void 0 ? void 0 : _a.create('plugin::strapi-custom-images-plugin.image-context-localized', {
                data: body,
                populate: ['image'],
            }));
            return entity;
        }
        catch (error) {
            ctx.throw(500, error);
        }
    },
    async update(ctx) {
        var _a;
        try {
            const { id } = ctx.params;
            const { body } = ctx.request;
            const entity = await ((_a = strapi.entityService) === null || _a === void 0 ? void 0 : _a.update('plugin::strapi-custom-images-plugin.image-context-localized', id, {
                data: body,
                populate: ['image'],
            }));
            if (!entity) {
                return ctx.notFound('Image context localized not found');
            }
            return entity;
        }
        catch (error) {
            ctx.throw(500, error);
        }
    },
    async delete(ctx) {
        var _a;
        try {
            const { id } = ctx.params;
            const entity = await ((_a = strapi.entityService) === null || _a === void 0 ? void 0 : _a.delete('plugin::strapi-custom-images-plugin.image-context-localized', id));
            if (!entity) {
                return ctx.notFound('Image context localized not found');
            }
            return { message: 'Image context localized deleted successfully' };
        }
        catch (error) {
            ctx.throw(500, error);
        }
    },
});
