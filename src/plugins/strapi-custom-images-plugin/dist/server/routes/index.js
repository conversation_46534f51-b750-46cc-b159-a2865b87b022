"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = [
    {
        method: 'GET',
        path: '/custom-images',
        handler: 'customImageControllers.find',
        config: {
            policies: [],
        },
    },
    {
        method: 'GET',
        path: '/custom-images/:id',
        handler: 'customImageControllers.findOne',
        config: {
            policies: [],
        },
    },
    {
        method: 'POST',
        path: '/custom-images',
        handler: 'customImageControllers.create',
        config: {
            policies: [],
        },
    },
    {
        method: 'PUT',
        path: '/custom-images/:id',
        handler: 'customImageControllers.update',
        config: {
            policies: [],
        },
    },
    {
        method: 'DELETE',
        path: '/custom-images/:id',
        handler: 'customImageControllers.delete',
        config: {
            policies: [],
        },
    },
];
