import { Strapi } from '@strapi/strapi';

export default ({ strapi }: { strapi: Strapi }) => ({
  async find(params) {
    return await strapi.entityService?.findMany('plugin::strapi-custom-images-plugin.image-context-localized', {
      ...params,
      populate: ['image', 'localizations'],
    });
  },

  async findOne(id, params) {
    return await strapi.entityService?.findOne('plugin::strapi-custom-images-plugin.image-context-localized', id, {
      ...params,
      populate: ['image', 'localizations'],
    });
  },

  async create(data) {
    return await strapi.entityService?.create('plugin::strapi-custom-images-plugin.image-context-localized', {
      data,
      populate: ['image'],
    });
  },

  async update(id, data) {
    return await strapi.entityService?.update('plugin::strapi-custom-images-plugin.image-context-localized', id, {
      data,
      populate: ['image'],
    });
  },

  async delete(id) {
    return await strapi.entityService?.delete('plugin::strapi-custom-images-plugin.image-context-localized', id);
  },

  async getLocalizedDataByImageId({
    imageId,
    locale,
    pageId,
  }) {
    locale = locale || 'en';
    const customImagesFound = await strapi.entityService?.findMany('plugin::strapi-custom-images-plugin.image-context-localized', {
      filters: {
        image: {
          id: imageId
        }
      },
      locale: locale,
      populate: ['image', 'captionByPage', 'captionByPage.page'],
    });
    const customImageFound = customImagesFound?.[0]
    console.log('getLocalizedDataByImageId customImageFound: ', JSON.stringify(customImageFound, null, 2));

    if (!customImageFound) {
      return null;
    }

    if (pageId) {
      const imageByPageFound = customImageFound.captionByPage?.find((c: any) => c.page?.id === pageId);
      if (imageByPageFound) {
        return {
          name: customImageFound.name,
          image: customImageFound.image,
          alt: imageByPageFound.alt,
          caption: imageByPageFound.caption,
        };
      }
    }

    return {
      name: customImageFound.name,
      image: customImageFound.image,
      alt: customImageFound.alt,
      caption: customImageFound.caption,
    };
  }

});
