"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.findCronJobByIdentifier = exports.createCronJobName = exports.parseCronJobUniqueId = exports.createPageUniqueIdentifier = void 0;
/**
 * Creates a unique identifier for a page based on its properties
 * This identifier should be stable across deletions and recreations
 *
 * @param page - The page object with type, name/title, country, and locale properties
 * @returns A unique string identifier for the page
 */
const createPageUniqueIdentifier = (page) => {
    var _a, _b;
    const countryCode = ((_b = (_a = page.country) === null || _a === void 0 ? void 0 : _a[0]) === null || _b === void 0 ? void 0 : _b.codeAlpha2) || 'nocountry';
    const locale = page.locale || 'en';
    let type = page.type || 'unknown';
    if (type.includes('Country')) {
        type = type.replace('Custom', '');
    }
    return {
        pageUniqueId: `${page.id}::${type}::${countryCode}::${locale}`,
        secondaryId: `${type}::${countryCode}::${locale}`
    };
};
exports.createPageUniqueIdentifier = createPageUniqueIdentifier;
/**
 * Parses a cron-job name to extract the unique identifier
 *
 * @param cronJobName - The cron-job name in format "model::uniqueId"
 * @returns The unique identifier part of the name
 */
const parseCronJobUniqueId = (cronJobName) => {
    const parts = cronJobName.split('::');
    if (parts.length >= 2) {
        // Remove the model part and return the rest as the unique identifier
        return parts.slice(1).join('::');
    }
    return null;
};
exports.parseCronJobUniqueId = parseCronJobUniqueId;
/**
 * Creates a cron-job name using the model and unique identifier
 *
 * @param model - The model UID (e.g., "api::page.page")
 * @param uniqueId - The unique identifier for the page
 * @returns The complete cron-job name
 */
const createCronJobName = (model, uniqueId) => {
    return `${model}::${uniqueId}`;
};
exports.createCronJobName = createCronJobName;
const findCronJobByIdentifier = async ({ pageFound, taskPrefixName, countryCode, localeCode }) => {
    const oldTaskName = `${taskPrefixName}::${countryCode}::${localeCode}`;
    let response = await strapi.service(`plugin::strapi-plugin-cron.cron-job`).getByFilter({ name: oldTaskName });
    let taskFound = response ? response[0] : null;
    const { pageUniqueId, secondaryId } = (0, exports.createPageUniqueIdentifier)(pageFound);
    const taskName = (0, exports.createCronJobName)("api::page.page", pageUniqueId);
    let taskByName = null;
    if (!taskFound) {
        response = await strapi.service(`plugin::strapi-plugin-cron.cron-job`).getByFilter({ name: taskName });
        taskByName = response ? response[0] : null;
        taskFound = response ? response[0] : null;
    }
    if (!taskFound && pageFound.type.includes('Country')) {
        response = await strapi.service(`plugin::strapi-plugin-cron.cron-job`).getByFilter({ name: { $containsi: secondaryId } });
        taskFound = response ? response[0] : null;
    }
    if (taskFound) {
        console.log('taskFound: ', taskFound.id, taskFound.name);
        if (!taskByName) {
            response = await strapi.service(`plugin::strapi-plugin-cron.cron-job`).getByFilter({ name: taskName });
            taskByName = response ? response[0] : null;
        }
        console.log('taskName: ', taskName, taskByName === null || taskByName === void 0 ? void 0 : taskByName.id, taskByName === null || taskByName === void 0 ? void 0 : taskByName.name);
        if ((taskByName === null || taskByName === void 0 ? void 0 : taskByName.id) && taskByName.id !== taskFound.id) {
            await strapi.service(`plugin::strapi-plugin-cron.cron-job`).delete(taskByName.id);
        }
    }
    return {
        taskName,
        taskFound
    };
};
exports.findCronJobByIdentifier = findCronJobByIdentifier;
