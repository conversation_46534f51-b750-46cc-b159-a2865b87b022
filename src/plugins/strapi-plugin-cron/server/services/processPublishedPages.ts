import { Strapi } from '@strapi/strapi';
import { GenericPageTypes, processPageByType } from './handleTasksByModel';

const ValidLocales = ['en', 'de', 'pt', 'fr', 'es'];

export default ({ strapi }: { strapi: Strapi }) => {
  const getAvailableLocales = async () => {
    try {
      // Get all available locales from Strapi i18n plugin
      const locales = await strapi.entityService.findMany('plugin::i18n.locale');
      return locales.map((locale: any) => locale.code);
    } catch (error) {
      console.error('Error fetching available locales:', error);
      // Fallback to default locales if i18n plugin is not available
      return ValidLocales;
    }
  };

  const run = async ({ locale }) => {
    console.log('processPublishedPages: run() called with locale:', locale);
    
    if (!locale) {
      // Get all available locales dynamically
      locale = await getAvailableLocales();
    } else if (!Array.isArray(locale)) {
      locale = [locale];
    }

    const promises = locale
      .filter((l: string) => ValidLocales.includes(l))
      .map((l: string) => strapi.entityService.findMany('api::page.page', {
        filters: {
          publishedAt: { $notNull: true },
          type: { $in: GenericPageTypes }
        },
        locale: l,
        populate: ['deep'],
      }) || []);

    const pagesByLocale = await Promise.all(promises);

    for (const pages of pagesByLocale) {
      for (const page of pages) {
        const elementId = page.id;
        const modelUid = 'api::page.page';
        const taskPrefixName = `${modelUid}::${elementId}`;
        const nameFound = page.name || page.title;
        const displayName = nameFound ? `${modelUid}::${nameFound}` : taskPrefixName;
        await processPageByType({
          page,
          displayName,
          taskPrefixName,
        });
      }
    }
  };
  return { run };
}; 