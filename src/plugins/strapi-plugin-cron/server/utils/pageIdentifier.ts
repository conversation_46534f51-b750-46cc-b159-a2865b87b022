/**
 * Creates a unique identifier for a page based on its properties
 * This identifier should be stable across deletions and recreations
 * 
 * @param page - The page object with type, name/title, country, and locale properties
 * @returns A unique string identifier for the page
 */
export const createPageUniqueIdentifier = (page: any): { pageUniqueId: string, secondaryId: string } => {
  const countryCode = page.country?.[0]?.codeAlpha2 || 'nocountry';
  const locale = page.locale || 'en';
  let type = page.type || 'unknown';

  if (type.includes('Country')) {
    type = type.replace('Custom', '');
  }

  return {
    pageUniqueId: `${page.id}::${type}::${countryCode}::${locale}`,
    secondaryId: `${type}::${countryCode}::${locale}`
  };
};

/**
 * Parses a cron-job name to extract the unique identifier
 * 
 * @param cronJobName - The cron-job name in format "model::uniqueId"
 * @returns The unique identifier part of the name
 */
export const parseCronJobUniqueId = (cronJobName: string): string | null => {
  const parts = cronJobName.split('::');
  if (parts.length >= 2) {
    // Remove the model part and return the rest as the unique identifier
    return parts.slice(1).join('::');
  }
  return null;
};

/**
 * Creates a cron-job name using the model and unique identifier
 * 
 * @param model - The model UID (e.g., "api::page.page")
 * @param uniqueId - The unique identifier for the page
 * @returns The complete cron-job name
 */
export const createCronJobName = (model: string, uniqueId: string): string => {
  return `${model}::${uniqueId}`;
};

export const findCronJobByIdentifier = async ({ pageFound, taskPrefixName, countryCode, localeCode }) => {
  const oldTaskName = `${taskPrefixName}::${countryCode}::${localeCode}`;

  let response = await (strapi.service(`plugin::strapi-plugin-cron.cron-job`) as any).getByFilter({ name: oldTaskName });
  let taskFound = response ? response[0] : null;

  const { pageUniqueId, secondaryId } = createPageUniqueIdentifier(pageFound);
  const taskName = createCronJobName("api::page.page", pageUniqueId);
  let taskByName = null;

  if (!taskFound) {
    response = await (strapi.service(`plugin::strapi-plugin-cron.cron-job`) as any).getByFilter({ name: taskName });
    taskByName = response ? response[0] : null;
    taskFound = response ? response[0] : null;
  }

  if (!taskFound && pageFound.type.includes('Country')) {
    response = await (strapi.service(`plugin::strapi-plugin-cron.cron-job`) as any).getByFilter({ name: { $containsi: secondaryId } });
    taskFound = response ? response[0] : null;
  }

  if (taskFound) {
    console.log('taskFound: ', taskFound.id, taskFound.name);
    if (!taskByName) {
      response = await (strapi.service(`plugin::strapi-plugin-cron.cron-job`) as any).getByFilter({ name: taskName });
      taskByName = response ? response[0] : null;
    }
    console.log('taskName: ', taskName, taskByName?.id, taskByName?.name);
    if (taskByName?.id && taskByName.id !== taskFound.id) {
      await (strapi.service(`plugin::strapi-plugin-cron.cron-job`) as any).delete(taskByName.id);
    }
  }

  return {
    taskName,
    taskFound
  }
}