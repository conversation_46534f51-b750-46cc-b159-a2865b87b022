import { useEffect, useRef } from 'react';
import isEqual from 'lodash/isEqual';

/**
 * A custom useEffect hook that only triggers when dependencies change based on deep comparison.
 * This is useful for objects and arrays that may have the same content but different references.
 */
export function useDeepCompareEffect(callback: () => void, dependencies: any) {
  const previousDependenciesRef = useRef();

  // Check if dependencies changed using deep comparison
  if (!isEqual(previousDependenciesRef.current, dependencies)) {
    previousDependenciesRef.current = dependencies;
  }

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(callback, [previousDependenciesRef.current]);
}