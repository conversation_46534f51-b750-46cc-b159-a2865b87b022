import React, { useCallback, useEffect, useMemo, useState, useRef } from 'react';
import { TextInput } from "@strapi/design-system";
import { useCMEditViewDataManager } from "@strapi/helper-plugin";
import { useQueryParams } from "@strapi/helper-plugin";
import { useDeepCompareEffect } from '../../hooks/useDeepCompareEffect';
import { useStableData } from '../../hooks/useStableData';
import UrlServiceApi from '../../api/url';
import PageServiceApi from '../../api/pages';
import { sanitizeCountryName, extractUrlPrefix } from '../../utils/urlHelpers';
import { auth } from '@strapi/helper-plugin';

interface CustomUrlProps {
    name: string;
    value: string;
    onChange: (event: { target: { name: string; value: string; type: string } }) => void;
    disabled?: boolean;
    placeholder?: string;
    attribute: { type: string };
    required?: boolean;
}

const CustomUrl: React.FC<CustomUrlProps> = (props) => {
    const { value, onChange, disabled, placeholder, attribute, required, name } = props;
    const { modifiedData, initialData } = useCMEditViewDataManager();
    const [queryParams] = useQueryParams();

    // Only apply rules if type is a service name
    const isServiceType = modifiedData.type?.includes('Country');

    // Use stable data to prevent reacting to intermediate changes
    const { isStable } = useStableData(modifiedData);

    // Rest of your state
    const [existingUrl, setExistingUrl] = useState<boolean>(false);
    const [isUrlCheckPending, setIsUrlCheckPending] = useState<boolean>(false);
    const [isGeneratingUrl, setIsGeneratingUrl] = useState<boolean>(false);
    const previousTypeRef = useRef<string | null>(null);
    const previousCountryRef = useRef<number | null>(null);
    const urlGenerationAttemptedRef = useRef<boolean>(false);
    const operationsEnabledRef = useRef<boolean>(false);

    // Enable operations after initial stabilization
    useEffect(() => {
        if (isStable && !operationsEnabledRef.current) {
            operationsEnabledRef.current = true;
            console.log('Data stabilized, enabling operations');
        }
    }, [isStable]);

    // Get the current locale from either modifiedData or query params
    const locale = useMemo(() => {
        let localeValue = modifiedData.locale;
        if (!localeValue) {
            const query = queryParams.query;
            localeValue = query?.plugins?.i18n?.locale;
        }
        return localeValue;
    }, [modifiedData.locale, queryParams.query]);

    // Get the selected country from modifiedData
    const country = useMemo(() => {
        return modifiedData.country?.[0];
    }, [modifiedData.country]);

    // Check if the current page type is a custom country page
    const isCustomCountryPage = useMemo(() => {
        return modifiedData.type?.includes('Custom');
    }, [modifiedData.type]);

    // Final disabled logic
    const isUrlDisabled = useMemo(() => {
        if (disabled || isServiceType) {
            console.log('disabled or isServiceType: ', { disabled, isServiceType });
            return true;
        }
        return false;
    }, [isServiceType, disabled]);

    // Handle URL input changes
    const handleInputChange = useCallback((newValue: string) => {
        onChange({
            target: {
                name,
                value: newValue,
                type: attribute.type,
            },
        });

        // Mark that we need to check this URL
        setIsUrlCheckPending(true);
    }, [name, onChange, attribute.type]);

    // Determine error message based on validation state
    const error = useMemo(() => {
        if (!value && required) {
            return "URL is required";
        }

        if (value && existingUrl) {
            return "The same URL is already in use by another page";
        }

        return undefined;
    }, [value, existingUrl, required]);

    // Generate URL based on country when page type changes to Custom
    const generateUrlFromCountry = useCallback(async () => {
        if (!isServiceType || !locale || !modifiedData.customCountry || !country) {
            return false;
        }

        try {
            setIsGeneratingUrl(true);

            // First try: Get URLs for the selected custom country
            const { results } = await UrlServiceApi.getAllUrl({
                locale,
                filters: [
                    {
                        directive: '[$eq]',
                        field: '[pageType]',
                        value: modifiedData.type.replace('Custom', '')
                    },
                    {
                        directive: '[$eq]',
                        field: '[country][id]',
                        value: modifiedData.customCountry
                    },
                ]
            });

            // Check if we found a URL for this country
            const countryUrlFound = results.find(({ url }: any) =>
                !!url && `${country.id}` === `${modifiedData.customCountry}`
            )?.url;

            if (countryUrlFound) {
                // Use the found URL
                handleInputChange(countryUrlFound);
                return true;
            }

            handleInputChange('');
            return true;

        } catch (error) {
            console.error("Error generating URL:", error);
            return false;
        } finally {
            setIsGeneratingUrl(false);
        }
    }, [
        isServiceType,
        locale,
        modifiedData.customCountry,
        modifiedData.type,
        country,
        value,
        handleInputChange
    ]);

    // Check if URL already exists
    useEffect(() => {
        if (!value || !locale || !isUrlCheckPending) return;

        let isMounted = true;
        const checkUrlExists = async () => {
            try {
                const { results } = await PageServiceApi.getPagesByUrl({
                    locale,
                    url: value
                });

                // Only update state if component is still mounted
                if (isMounted) {
                    // URL exists if we find any page with this URL that isn't the current page
                    const urlExists = results.some((p: any) => p.id !== modifiedData.id);
                    setExistingUrl(urlExists);
                    setIsUrlCheckPending(false);
                }
            } catch (error) {
                console.error("Error checking URL existence:", error);
                if (isMounted) {
                    setIsUrlCheckPending(false);
                }
            }
        };

        // Debounce the URL check to avoid too many requests
        const timer = setTimeout(checkUrlExists, 300);
        return () => {
            isMounted = false;
            clearTimeout(timer);
        };
    }, [value, locale, modifiedData.id, isUrlCheckPending]);

    // Use deep compare effect for operations that should only run when data truly changes
    useEffect(() => {
        // Skip operations until data is stable
        if (!operationsEnabledRef.current) {
            return;
        }

        const currentType = modifiedData.type;
        const previousType = previousTypeRef.current;

        // Update the reference for next time
        previousTypeRef.current = currentType;

        // If this is the first render, just check if we need to generate URL
        if (!previousType) {
            if (isCustomCountryPage && modifiedData.customCountry && country && !value) {
                urlGenerationAttemptedRef.current = true;
                generateUrlFromCountry();
            }
            return;
        }

        // Handle type changes
        if (previousType !== currentType) {
            // Reset URL generation flag when type changes
            urlGenerationAttemptedRef.current = false;

            // If changing to a custom country page, generate URL
            if (isCustomCountryPage && modifiedData.customCountry && country) {
                urlGenerationAttemptedRef.current = true;
                generateUrlFromCountry();
            }
            // If changing to a non-custom page, clear URL if it was auto-generated
            else if (previousType?.includes('Custom') && !currentType?.includes('Custom')) {
                // We don't clear the URL here as it might be manually set
                // Just reset the flag
                urlGenerationAttemptedRef.current = false;
            }
        }
    }, [
        modifiedData.type,
        isCustomCountryPage,
        modifiedData.customCountry,
        country,
        value,
        generateUrlFromCountry
    ]);

    useEffect(() => {
        if (!isServiceType && initialData.type?.includes('Country')) {
            onChange({
                target: {
                    name,
                    value: '',
                    type: attribute.type
                }
            });
        }
    }, [modifiedData.type])

    // Generate URL when country changes for custom pages
    useEffect(() => {
        // Track country changes
        const currentCountryId = country?.id;
        const previousCountryId = previousCountryRef.current;

        // Update reference for next time
        previousCountryRef.current = currentCountryId;

        // If country has changed and we're on a custom page, regenerate URL
        if (isCustomCountryPage && modifiedData.customCountry &&
            currentCountryId && previousCountryId !== currentCountryId) {
            generateUrlFromCountry();
        }
        // If no previous attempt and we have all needed data, generate URL
        else if (isCustomCountryPage && modifiedData.customCountry &&
            country && !urlGenerationAttemptedRef.current) {
            urlGenerationAttemptedRef.current = true;
            generateUrlFromCountry();
        } else if (!isCustomCountryPage && isServiceType) {
            generateUrlFromService();
        }
    }, [
        isCustomCountryPage, 
        isServiceType, 
        modifiedData.customCountry, 
        country, 
        generateUrlFromCountry
    ]);

    return (
        <TextInput
            label="URL"
            required={required}
            placeholder={placeholder || 'Enter URL'}
            value={value || ''}
            aria-label="URL input"
            name={name}
            disabled={isUrlDisabled}
            loading={isGeneratingUrl}
            style={{ width: '100%' }}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => {
                handleInputChange(e.target.value);
            }}
            error={error}
        />
    );
};

export default React.memo(CustomUrl);
