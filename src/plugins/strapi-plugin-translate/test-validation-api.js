#!/usr/bin/env node

/**
 * Test script to verify validation changes API endpoints
 * This script tests the API endpoints to ensure they're working correctly
 */

// Mock strapi for testing
global.strapi = {
  log: {
    info: console.log,
    error: console.error,
    warn: console.warn,
    debug: console.debug
  }
}

const validationChangesService = require('./server/services/validation-changes')({ strapi })

console.log('🧪 Testing Validation Changes API')
console.log('=' .repeat(50))

// Test data
const testContentType = 'api::page.page'
const testLocale = 'es'
const testEntityId = 123
const testChanges = [
  {
    type: 'truncation',
    field: 'seo.metaDescription',
    originalLength: 291,
    newLength: 235,
    method: 'sentence',
    message: "Truncated 'seo.metaDescription' from 291 to 235 characters using sentence boundary"
  }
]

async function testValidationChangesService() {
  console.log('\n📝 Testing Validation Changes Service...')
  
  try {
    // Test 1: Store validation changes
    console.log('\n1. Testing storeValidationChanges...')
    const storeResult = validationChangesService.storeValidationChanges(
      testContentType,
      testLocale,
      testEntityId,
      testChanges
    )
    console.log(`✅ Store result: ${storeResult.success ? 'SUCCESS' : 'FAILED'}`)
    console.log(`📊 Stored changes: ${storeResult.totalChanges}`)
    
    // Test 2: Check if changes exist
    console.log('\n2. Testing hasValidationChanges...')
    const hasChanges = validationChangesService.hasValidationChanges(testContentType, testLocale)
    console.log(`✅ Has changes: ${hasChanges ? 'YES' : 'NO'}`)
    
    // Test 3: Retrieve validation changes
    console.log('\n3. Testing getValidationChanges...')
    const retrievedChanges = validationChangesService.getValidationChanges(testContentType, testLocale)
    console.log(`✅ Retrieval result: ${retrievedChanges ? 'SUCCESS' : 'FAILED'}`)
    if (retrievedChanges) {
      console.log(`📊 Retrieved changes: ${retrievedChanges.changes.length}`)
      console.log(`🔍 Has changes flag: ${retrievedChanges.hasChanges}`)
      console.log(`📋 Changes by type:`, Object.keys(retrievedChanges.changesByType))
      console.log(`📋 Changes by field:`, Object.keys(retrievedChanges.changesByField))
    }
    
    // Test 4: Get all changes for content type
    console.log('\n4. Testing getAllValidationChangesForContentType...')
    const allChanges = validationChangesService.getAllValidationChangesForContentType(testContentType)
    console.log(`✅ All changes result: ${Object.keys(allChanges).length > 0 ? 'SUCCESS' : 'FAILED'}`)
    console.log(`📊 Locales with changes: ${Object.keys(allChanges).join(', ')}`)
    
    // Test 5: Format for frontend
    console.log('\n5. Testing formatChangesForFrontend...')
    const formatted = validationChangesService.formatChangesForFrontend(
      testChanges,
      testContentType,
      testLocale
    )
    console.log(`✅ Format result: ${formatted.formattedChanges.length > 0 ? 'SUCCESS' : 'FAILED'}`)
    console.log(`📊 Formatted changes: ${formatted.formattedChanges.length}`)
    console.log(`📋 Summary:`, formatted.summary)
    
    // Test 6: Clear validation changes
    console.log('\n6. Testing clearValidationChanges...')
    const cleared = validationChangesService.clearValidationChanges(testContentType, testLocale)
    console.log(`✅ Clear result: ${cleared ? 'SUCCESS' : 'FAILED'}`)
    
    // Test 7: Verify cleared
    console.log('\n7. Verifying changes were cleared...')
    const afterClear = validationChangesService.hasValidationChanges(testContentType, testLocale)
    console.log(`✅ Verification: ${afterClear ? 'STILL EXISTS (ERROR)' : 'CLEARED (SUCCESS)'}`)
    
    console.log('\n🎉 All validation changes service tests completed!')
    
  } catch (error) {
    console.error('\n❌ Service test failed!')
    console.error('Error:', error.message)
    console.error('Stack:', error.stack)
    return false
  }
  
  return true
}

async function testAPIEndpoints() {
  console.log('\n🌐 Testing API Endpoint Logic...')
  
  // Mock controller context
  const mockCtx = {
    request: {
      query: {},
      body: {}
    },
    body: null,
    badRequest: (msg) => { throw new Error(`BadRequest: ${msg}`) },
    notFound: (msg) => { throw new Error(`NotFound: ${msg}`) }
  }
  
  // Mock getService function
  const mockGetService = (serviceName) => {
    if (serviceName === 'validation-changes') {
      return validationChangesService
    }
    throw new Error(`Unknown service: ${serviceName}`)
  }
  
  try {
    // Re-add test data for API tests
    validationChangesService.storeValidationChanges(
      testContentType,
      testLocale,
      testEntityId,
      testChanges
    )
    
    // Test getValidationChanges endpoint logic
    console.log('\n1. Testing getValidationChanges endpoint logic...')
    mockCtx.request.query = { contentType: testContentType, locale: testLocale }
    
    const validationChanges = await mockGetService('validation-changes').getValidationChanges(
      mockCtx.request.query.contentType,
      mockCtx.request.query.locale
    )
    
    if (validationChanges) {
      mockCtx.body = { data: validationChanges }
      console.log('✅ getValidationChanges endpoint: SUCCESS')
      console.log(`📊 Response data has changes: ${mockCtx.body.data.hasChanges}`)
    } else {
      console.log('❌ getValidationChanges endpoint: FAILED - No data returned')
    }
    
    // Test getAllValidationChangesForContentType endpoint logic
    console.log('\n2. Testing getAllValidationChangesForContentType endpoint logic...')
    mockCtx.request.query = { contentType: testContentType }
    
    const allValidationChanges = await mockGetService('validation-changes').getAllValidationChangesForContentType(
      mockCtx.request.query.contentType
    )
    
    mockCtx.body = { data: allValidationChanges }
    console.log('✅ getAllValidationChangesForContentType endpoint: SUCCESS')
    console.log(`📊 Response locales: ${Object.keys(mockCtx.body.data).join(', ')}`)
    
    // Test clearValidationChanges endpoint logic
    console.log('\n3. Testing clearValidationChanges endpoint logic...')
    mockCtx.request.body = { contentType: testContentType, locale: testLocale }
    
    const cleared = await mockGetService('validation-changes').clearValidationChanges(
      mockCtx.request.body.contentType,
      mockCtx.request.body.locale
    )
    
    mockCtx.body = {
      data: {
        cleared,
        contentType: mockCtx.request.body.contentType,
        locale: mockCtx.request.body.locale
      }
    }
    console.log('✅ clearValidationChanges endpoint: SUCCESS')
    console.log(`📊 Cleared: ${mockCtx.body.data.cleared}`)
    
    console.log('\n🎉 All API endpoint tests completed!')
    
  } catch (error) {
    console.error('\n❌ API endpoint test failed!')
    console.error('Error:', error.message)
    console.error('Stack:', error.stack)
    return false
  }
  
  return true
}

async function runAllTests() {
  console.log('🚀 Starting comprehensive validation changes tests...\n')
  
  const serviceTestResult = await testValidationChangesService()
  const apiTestResult = await testAPIEndpoints()
  
  console.log('\n📋 Test Summary:')
  console.log(`✅ Validation Changes Service: ${serviceTestResult ? 'PASSED' : 'FAILED'}`)
  console.log(`✅ API Endpoint Logic: ${apiTestResult ? 'PASSED' : 'FAILED'}`)
  
  if (serviceTestResult && apiTestResult) {
    console.log('\n🎉 All tests PASSED! The validation changes system is working correctly.')
    console.log('\n🔍 Key findings:')
    console.log('- Validation changes can be stored and retrieved')
    console.log('- hasValidationChanges correctly detects existence')
    console.log('- API endpoint logic works as expected')
    console.log('- Data formatting for frontend is functional')
    console.log('- Clear functionality works properly')
  } else {
    console.log('\n❌ Some tests FAILED. Please check the errors above.')
    process.exit(1)
  }
}

// Run the tests
runAllTests().catch(error => {
  console.error('Fatal error running tests:', error)
  process.exit(1)
})
