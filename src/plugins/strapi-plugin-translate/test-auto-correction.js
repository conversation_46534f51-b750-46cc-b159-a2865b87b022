#!/usr/bin/env node
'use strict'

// Test auto-correction functionality
const { 
  truncateText, 
  padText, 
  autoCorrectTranslatedContent,
  getTextLength,
  containsHTML,
  extractKeywords,
  expandContent
} = require('./server/utils/text-auto-correction')

const { autoCorrectTranslatedContent: autoCorrectFromValidation } = require('./server/utils/field-validation')

// Mock strapi global
global.strapi = {
  components: {
    'shared.seo': {
      attributes: {
        metaTitle: {
          type: 'string',
          required: true,
          maxLength: 70
        },
        metaDescription: {
          type: 'text',
          required: true,
          maxLength: 250
        },
        keywords: {
          type: 'text',
          minLength: 10
        }
      }
    },
    'shared.meta-social': {
      attributes: {
        title: {
          type: 'string',
          required: true,
          maxLength: 60
        },
        description: {
          type: 'string',
          required: true,
          maxLength: 65
        }
      }
    }
  },
  contentTypes: {
    'api::page.page': {
      attributes: {
        title: {
          type: 'string',
          required: true,
          minLength: 5
        },
        seo: {
          type: 'component',
          component: 'shared.seo',
          repeatable: false
        },
        content: {
          type: 'dynamiczone',
          components: ['shared.meta-social']
        }
      }
    }
  },
  log: {
    error: console.error,
    warn: console.warn,
    debug: console.debug,
    info: console.info
  }
}

function runAutoCorrectTests() {
  console.log('🧪 Running Auto-Correction Tests...\n')
  
  let passed = 0
  let failed = 0
  
  function test(name, testFn) {
    try {
      testFn()
      console.log(`✅ ${name}`)
      passed++
    } catch (error) {
      console.log(`❌ ${name}`)
      console.log(`   Error: ${error.message}`)
      failed++
    }
  }
  
  function expect(actual) {
    return {
      toBe: (expected) => {
        if (actual !== expected) {
          throw new Error(`Expected ${expected}, got ${actual}`)
        }
      },
      toEqual: (expected) => {
        if (JSON.stringify(actual) !== JSON.stringify(expected)) {
          throw new Error(`Expected ${JSON.stringify(expected)}, got ${JSON.stringify(actual)}`)
        }
      },
      toBeTrue: () => {
        if (actual !== true) {
          throw new Error(`Expected true, got ${actual}`)
        }
      },
      toBeFalse: () => {
        if (actual !== false) {
          throw new Error(`Expected false, got ${actual}`)
        }
      },
      toContain: (substring) => {
        if (!actual || !actual.includes(substring)) {
          throw new Error(`Expected "${actual}" to contain "${substring}"`)
        }
      },
      toBeLessThanOrEqual: (max) => {
        if (actual > max) {
          throw new Error(`Expected ${actual} to be <= ${max}`)
        }
      },
      toBeGreaterThanOrEqual: (min) => {
        if (actual < min) {
          throw new Error(`Expected ${actual} to be >= ${min}`)
        }
      }
    }
  }
  
  // Test truncateText function
  test('truncateText - basic truncation', () => {
    const result = truncateText('This is a very long text that needs to be truncated', 20)
    expect(result.truncated).toBeTrue()
    expect(result.newLength).toBeLessThanOrEqual(20)
    expect(result.text).toContain('...')
  })
  
  test('truncateText - HTML preservation', () => {
    const htmlText = '<p>This is a <strong>very long</strong> HTML text that needs truncation</p>'
    const result = truncateText(htmlText, 30, { preserveHTML: true })
    expect(result.truncated).toBeTrue()
    expect(result.text).toContain('<p>')
    expect(result.text).toContain('</p>')
  })
  
  test('truncateText - sentence boundary', () => {
    const text = 'First sentence. Second sentence. Third sentence that is very long.'
    const result = truncateText(text, 35)
    expect(result.method).toBe('sentence')
    expect(result.text).toContain('First sentence.')
  })
  
  // Test padText function
  test('padText - basic padding', () => {
    const result = padText('Short', 15, 'title')
    expect(result.padded).toBeTrue()
    expect(result.newLength).toBeGreaterThanOrEqual(15)
  })
  
  test('padText - contextual padding for metaTitle', () => {
    const result = padText('SEO Guide', 25, 'metaTitle')
    expect(result.padded).toBeTrue()
    expect(result.text).toContain('SEO Guide')
    expect(result.method).toContain('contextual')
  })
  
  test('padText - expansion strategy', () => {
    const result = padText('Good guide', 20, 'title', { strategy: 'expansion' })
    expect(result.padded).toBeTrue()
    expect(result.text.length).toBeGreaterThanOrEqual(20)
  })
  
  // Test utility functions
  test('getTextLength - HTML handling', () => {
    const htmlText = '<p>Hello <strong>world</strong>!</p>'
    const length = getTextLength(htmlText)
    expect(length).toBe(12) // "Hello world!" without HTML tags
  })
  
  test('containsHTML - detection', () => {
    expect(containsHTML('<p>Hello</p>')).toBeTrue()
    expect(containsHTML('Plain text')).toBeFalse()
  })
  
  test('extractKeywords - basic extraction', () => {
    const keywords = extractKeywords('Learn about SEO optimization and best practices')
    expect(keywords.length).toBeGreaterThanOrEqual(1)
    expect(keywords).toContain('seo')
  })
  
  test('expandContent - word replacement', () => {
    const expanded = expandContent('This is a good guide', 30)
    expect(expanded.length).toBeGreaterThanOrEqual('This is a good guide'.length)
  })
  
  // Test full auto-correction integration
  test('autoCorrectTranslatedContent - maxLength violation', () => {
    const data = {
      title: 'Valid Title',
      seo: {
        metaTitle: 'This is a very long meta title that definitely exceeds the 70 character limit set for SEO meta titles',
        metaDescription: 'Valid description'
      }
    }
    
    const result = autoCorrectFromValidation(data, 'api::page.page')
    expect(result.corrected).toBeTrue()
    expect(result.changes.length).toBeGreaterThanOrEqual(1)
    expect(getTextLength(result.data.seo.metaTitle)).toBeLessThanOrEqual(70)
  })
  
  test('autoCorrectTranslatedContent - minLength violation', () => {
    const data = {
      title: 'Hi', // Too short (minLength: 5)
      seo: {
        metaTitle: 'Valid Meta Title',
        metaDescription: 'Valid description',
        keywords: 'SEO' // Too short (minLength: 10)
      }
    }

    const result = autoCorrectFromValidation(data, 'api::page.page')
    expect(result.corrected).toBeTrue()
    expect(result.changes.length).toBeGreaterThanOrEqual(2) // title and keywords
    expect(getTextLength(result.data.title)).toBeGreaterThanOrEqual(5)
    expect(getTextLength(result.data.seo.keywords)).toBeGreaterThanOrEqual(10)
  })
  
  test('autoCorrectTranslatedContent - dynamic zone correction', () => {
    const data = {
      title: 'Valid Title',
      content: [
        {
          __component: 'shared.meta-social',
          title: 'This is a very long social media title that exceeds the 60 character limit',
          description: 'Valid description'
        }
      ]
    }
    
    const result = autoCorrectFromValidation(data, 'api::page.page')
    expect(result.corrected).toBeTrue()
    expect(getTextLength(result.data.content[0].title)).toBeLessThanOrEqual(60)
  })
  
  test('autoCorrectTranslatedContent - no changes needed', () => {
    const data = {
      title: 'Valid Title',
      seo: {
        metaTitle: 'Valid Meta Title',
        metaDescription: 'Valid meta description',
        keywords: 'Valid keywords for SEO'
      }
    }
    
    const result = autoCorrectFromValidation(data, 'api::page.page')
    expect(result.corrected).toBeFalse()
    expect(result.changes.length).toBe(0)
  })
  
  test('autoCorrectTranslatedContent - HTML preservation in correction', () => {
    const htmlTitle = '<strong>This is a very long meta title with HTML</strong> that definitely exceeds the seventy character limit for SEO'

    const data = {
      title: 'Valid Title',
      seo: {
        metaTitle: htmlTitle,
        metaDescription: 'Valid description'
      }
    }

    const result = autoCorrectFromValidation(data, 'api::page.page')
    expect(result.corrected).toBeTrue()
    expect(result.data.seo.metaTitle).toContain('<strong>')
    expect(getTextLength(result.data.seo.metaTitle)).toBeLessThanOrEqual(70)
  })
  
  console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`)
  
  if (failed === 0) {
    console.log('🎉 All auto-correction tests passed!')
    return true
  } else {
    console.log('💥 Some auto-correction tests failed!')
    return false
  }
}

if (require.main === module) {
  const success = runAutoCorrectTests()
  process.exit(success ? 0 : 1)
}

module.exports = { runAutoCorrectTests }
