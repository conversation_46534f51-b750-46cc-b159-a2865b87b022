#!/usr/bin/env node

/**
 * Debug script to test ValidationChangesModal data structure compatibility
 * This script simulates the data flow from backend to frontend modal
 */

console.log('🔍 Debug: ValidationChangesModal Data Structure Test')
console.log('=' * 60)

// Mock Strapi service structure
const mockStrapi = {
  log: {
    info: (msg) => console.log(`[INFO] ${msg}`),
    debug: (msg) => console.log(`[DEBUG] ${msg}`),
    warn: (msg) => console.log(`[WARN] ${msg}`),
    error: (msg) => console.log(`[ERROR] ${msg}`)
  }
}

// Import the validation-changes service
const validationChangesService = require('./server/services/validation-changes')({ strapi: mockStrapi })

// Test data
const testContentType = 'api::page.page'
const testLocale = 'de'
const testEntityId1 = 491
const testEntityId2 = 492

const testChanges1 = [
  {
    type: 'length_truncation',
    field: 'seo.metaDescription',
    message: "Truncated 'seo.metaDescription' from 289 to 231 characters using sentence boundary",
    originalLength: 289,
    newLength: 231,
    truncationMethod: 'sentence_boundary'
  }
]

const testChanges2 = [
  {
    type: 'length_truncation', 
    field: 'title',
    message: "Truncated 'title' from 150 to 100 characters using word boundary",
    originalLength: 150,
    newLength: 100,
    truncationMethod: 'word_boundary'
  }
]

async function testModalDataStructure() {
  try {
    console.log('\n1. Storing test validation changes...')
    
    // Store first set of changes
    validationChangesService.storeValidationChanges(
      testContentType,
      testLocale,
      testEntityId1,
      testChanges1
    )
    
    // Store second set of changes (should accumulate)
    validationChangesService.storeValidationChanges(
      testContentType,
      testLocale,
      testEntityId2,
      testChanges2
    )
    
    console.log('\n2. Retrieving validation changes (simulating API call)...')
    
    // Simulate the API controller response
    const validationChanges = validationChangesService.getValidationChanges(testContentType, testLocale)
    
    console.log('📡 Backend API response structure:')
    console.log(JSON.stringify(validationChanges, null, 2))
    
    console.log('\n3. Testing frontend modal data access...')
    
    // Simulate frontend modal data access
    console.log('✅ Testing modal data access patterns:')
    
    // Test hasChanges
    console.log(`   hasChanges: ${validationChanges?.hasChanges}`)
    
    // Test totalChanges (direct property)
    console.log(`   totalChanges: ${validationChanges?.totalChanges}`)
    
    // Test changes array
    console.log(`   changes.length: ${validationChanges?.changes?.length}`)
    
    // Test changesByType
    console.log(`   changesByType keys: ${Object.keys(validationChanges?.changesByType || {})}`)
    
    // Test changesByField
    console.log(`   changesByField keys: ${Object.keys(validationChanges?.changesByField || {})}`)
    
    console.log('\n4. Testing modal summary rendering logic...')
    
    // Simulate the frontend summary rendering logic
    const totalChanges = validationChanges.totalChanges || validationChanges.changes?.length || 0
    const changesByType = validationChanges.changesByType || {}
    
    console.log(`   Computed totalChanges: ${totalChanges}`)
    console.log(`   Computed changesByType: ${JSON.stringify(changesByType)}`)
    
    // Test the byType mapping
    if (changesByType && Object.keys(changesByType).length > 0) {
      console.log('   Changes by type breakdown:')
      Object.entries(changesByType).forEach(([type, changes]) => {
        console.log(`     ${type}: ${changes.length} changes`)
      })
    }
    
    console.log('\n5. Testing individual change data...')
    
    if (validationChanges.changes && validationChanges.changes.length > 0) {
      validationChanges.changes.forEach((change, index) => {
        console.log(`   Change ${index + 1}:`)
        console.log(`     field: ${change.field}`)
        console.log(`     type: ${change.type}`)
        console.log(`     entityId: ${change.entityId}`)
        console.log(`     message: ${change.message}`)
      })
    }
    
    console.log('\n🎉 SUCCESS: All data structure tests passed!')
    console.log('The ValidationChangesModal should be able to render this data correctly.')
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test
testModalDataStructure()
