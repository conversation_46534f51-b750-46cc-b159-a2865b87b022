{"name": "strapi-plugin-translate", "version": "1.4.1", "description": "Strapi plugin for managing and automating translation of content", "keywords": ["strapi", "plugin", "translate", "translation", "i18n"], "maintainers": [], "scripts": {"lint": "eslint .", "test": "jest"}, "dependencies": {"@strapi/helper-plugin": "^4.15.0", "axios": "^1.7.4", "blocks-html-renderer": "^1.0.5", "bottleneck": "^2.19.5", "cache-manager": "^6.1.0", "jsdom": "^25.0.0", "showdown": "^2.1.0", "openai": "^4.47.2"}, "devDependencies": {"@faker-js/faker": "^8.4.0", "@strapi/plugin-i18n": "^4.15.0", "axios-mock-adapter": "^2.0.0", "lodash": "^4.17.21", "react": "^17.0 || ^18.0", "react-dom": "^17.0 || ^18.0", "react-router-dom": "^5.2.0"}, "peerDependencies": {"@strapi/plugin-i18n": "^4.1.4", "@strapi/strapi": "^4.1.4"}, "engines": {"node": ">=14 <=20", "npm": ">=6.0.0"}, "strapi": {"name": "translate", "description": "Manage and automate content translation", "kind": "plugin"}}