#!/usr/bin/env node

/**
 * Debug script to test validation changes storage and retrieval
 * This script simulates the validation changes workflow to identify issues
 */

console.log('🔍 Debug: Validation Changes Storage and Retrieval Test')
console.log('=' * 60)

// Mock Strapi service structure
const mockStrapi = {
  log: {
    info: (msg) => console.log(`[INFO] ${msg}`),
    debug: (msg) => console.log(`[DEBUG] ${msg}`),
    warn: (msg) => console.log(`[WARN] ${msg}`),
    error: (msg) => console.log(`[ERROR] ${msg}`)
  }
}

// Import the validation-changes service
const validationChangesService = require('./server/services/validation-changes')({ strapi: mockStrapi })

// Test data
const testContentType = 'api::page.page'
const testLocale = 'de'
const testEntityId1 = 491
const testEntityId2 = 492

const testChanges1 = [
  {
    type: 'length_truncation',
    field: 'seo.metaDescription',
    message: "Truncated 'seo.metaDescription' from 289 to 231 characters using sentence boundary",
    originalLength: 289,
    newLength: 231,
    truncationMethod: 'sentence_boundary'
  }
]

const testChanges2 = [
  {
    type: 'length_truncation', 
    field: 'title',
    message: "Truncated 'title' from 150 to 100 characters using word boundary",
    originalLength: 150,
    newLength: 100,
    truncationMethod: 'word_boundary'
  }
]

async function runTest() {
  try {
    console.log('\n1. Testing storage of validation changes...')
    
    // Store first set of changes
    console.log(`\n📝 Storing changes for entity ${testEntityId1}`)
    const result1 = validationChangesService.storeValidationChanges(
      testContentType,
      testLocale,
      testEntityId1,
      testChanges1
    )
    console.log('✅ Result 1:', result1)
    
    // Store second set of changes (should accumulate)
    console.log(`\n📝 Storing changes for entity ${testEntityId2}`)
    const result2 = validationChangesService.storeValidationChanges(
      testContentType,
      testLocale,
      testEntityId2,
      testChanges2
    )
    console.log('✅ Result 2:', result2)
    
    console.log('\n2. Testing retrieval of validation changes...')
    
    // Retrieve validation changes
    const retrievedChanges = validationChangesService.getValidationChanges(testContentType, testLocale)
    console.log('📥 Retrieved changes:', JSON.stringify(retrievedChanges, null, 2))
    
    console.log('\n3. Testing hasValidationChanges check...')
    
    // Check if validation changes exist
    const hasChanges = validationChangesService.hasValidationChanges(testContentType, testLocale)
    console.log(`🔍 Has validation changes: ${hasChanges}`)
    
    console.log('\n4. Testing API controller logic simulation...')
    
    // Simulate the API controller logic
    const mockCtx = {
      request: {
        query: {
          contentType: testContentType,
          locale: testLocale
        }
      },
      body: null
    }
    
    const validationChanges = validationChangesService.getValidationChanges(
      mockCtx.request.query.contentType,
      mockCtx.request.query.locale
    )
    
    // Simulate the controller response logic
    mockCtx.body = {
      data: validationChanges || {
        contentType: testContentType,
        locale: testLocale,
        hasChanges: false,
        changes: [],
        totalChanges: 0,
        timestamp: new Date().toISOString()
      }
    }
    
    console.log('📡 Simulated API response:', JSON.stringify(mockCtx.body, null, 2))
    
    console.log('\n5. Summary:')
    console.log(`✅ Validation changes stored: ${result2.totalChanges}`)
    console.log(`✅ Validation changes retrieved: ${retrievedChanges ? retrievedChanges.totalChanges : 0}`)
    console.log(`✅ Has changes check: ${hasChanges}`)
    console.log(`✅ API response has changes: ${mockCtx.body.data.hasChanges}`)
    
    if (hasChanges && mockCtx.body.data.hasChanges) {
      console.log('\n🎉 SUCCESS: Validation changes workflow is working correctly!')
    } else {
      console.log('\n❌ ISSUE: Validation changes workflow has problems!')
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error)
  }
}

// Run the test
runTest()
