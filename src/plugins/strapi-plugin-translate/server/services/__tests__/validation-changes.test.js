'use strict'

const validationChangesService = require('../validation-changes')

// Mock strapi
const mockStrapi = {
  log: {
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
    debug: jest.fn()
  }
}

describe('validation-changes service', () => {
  let service

  beforeEach(() => {
    service = validationChangesService({ strapi: mockStrapi })
    // Clear the store before each test
    service.validationChangesStore.clear()
    jest.clearAllMocks()
  })

  describe('storeValidationChanges', () => {
    test('should store validation changes successfully', () => {
      const contentType = 'api::page.page'
      const locale = 'es'
      const entityId = 123
      const changes = [
        {
          type: 'truncation',
          field: 'title',
          originalLength: 100,
          newLength: 50,
          message: 'Title was truncated'
        }
      ]

      const result = service.storeValidationChanges(contentType, locale, entityId, changes)

      expect(result.success).toBe(true)
      expect(result.totalChanges).toBe(1)
      expect(service.validationChangesStore.has(`${contentType}:${locale}`)).toBe(true)
    })

    test('should throw error for invalid parameters', () => {
      expect(() => {
        service.storeValidationChanges(null, 'es', 123, [])
      }).toThrow('Invalid parameters for storing validation changes')

      expect(() => {
        service.storeValidationChanges('api::page.page', null, 123, [])
      }).toThrow('Invalid parameters for storing validation changes')

      expect(() => {
        service.storeValidationChanges('api::page.page', 'es', 123, 'not-array')
      }).toThrow('Invalid parameters for storing validation changes')
    })
  })

  describe('getValidationChanges', () => {
    test('should retrieve stored validation changes', () => {
      const contentType = 'api::page.page'
      const locale = 'es'
      const entityId = 123
      const changes = [
        {
          type: 'truncation',
          field: 'title',
          originalLength: 100,
          newLength: 50,
          message: 'Title was truncated'
        }
      ]

      service.storeValidationChanges(contentType, locale, entityId, changes)
      const result = service.getValidationChanges(contentType, locale)

      expect(result).toBeTruthy()
      expect(result.hasChanges).toBe(true)
      expect(result.changes).toHaveLength(1)
      expect(result.changesByType.truncation).toHaveLength(1)
      expect(result.changesByField.title).toHaveLength(1)
    })

    test('should return null for non-existent changes', () => {
      const result = service.getValidationChanges('api::nonexistent.nonexistent', 'es')
      expect(result).toBeNull()
    })

    test('should return null for invalid parameters', () => {
      expect(service.getValidationChanges(null, 'es')).toBeNull()
      expect(service.getValidationChanges('api::page.page', null)).toBeNull()
    })
  })

  describe('clearValidationChanges', () => {
    test('should clear existing validation changes', () => {
      const contentType = 'api::page.page'
      const locale = 'es'
      const entityId = 123
      const changes = [{ type: 'truncation', field: 'title', message: 'Test' }]

      service.storeValidationChanges(contentType, locale, entityId, changes)
      expect(service.hasValidationChanges(contentType, locale)).toBe(true)

      const cleared = service.clearValidationChanges(contentType, locale)
      expect(cleared).toBe(true)
      expect(service.hasValidationChanges(contentType, locale)).toBe(false)
    })

    test('should return false for non-existent changes', () => {
      const cleared = service.clearValidationChanges('api::nonexistent.nonexistent', 'es')
      expect(cleared).toBe(false)
    })
  })

  describe('hasValidationChanges', () => {
    test('should return true when changes exist', () => {
      const contentType = 'api::page.page'
      const locale = 'es'
      const entityId = 123
      const changes = [{ type: 'truncation', field: 'title', message: 'Test' }]

      service.storeValidationChanges(contentType, locale, entityId, changes)
      expect(service.hasValidationChanges(contentType, locale)).toBe(true)
    })

    test('should return false when no changes exist', () => {
      expect(service.hasValidationChanges('api::nonexistent.nonexistent', 'es')).toBe(false)
    })
  })

  describe('getAllValidationChangesForContentType', () => {
    test('should return all changes for a content type across locales', () => {
      const contentType = 'api::page.page'
      const changes = [{ type: 'truncation', field: 'title', message: 'Test' }]

      service.storeValidationChanges(contentType, 'es', 123, changes)
      service.storeValidationChanges(contentType, 'fr', 124, changes)

      const result = service.getAllValidationChangesForContentType(contentType)

      expect(Object.keys(result)).toHaveLength(2)
      expect(result.es).toBeTruthy()
      expect(result.fr).toBeTruthy()
      expect(result.es.hasChanges).toBe(true)
      expect(result.fr.hasChanges).toBe(true)
    })

    test('should return empty object for non-existent content type', () => {
      const result = service.getAllValidationChangesForContentType('api::nonexistent.nonexistent')
      expect(result).toEqual({})
    })
  })

  describe('getValidationChangesSummary', () => {
    test('should return correct summary', () => {
      const contentType1 = 'api::page.page'
      const contentType2 = 'api::article.article'
      const changes = [
        { type: 'truncation', field: 'title', message: 'Test' },
        { type: 'padding', field: 'description', message: 'Test' }
      ]

      service.storeValidationChanges(contentType1, 'es', 123, changes)
      service.storeValidationChanges(contentType2, 'fr', 124, [changes[0]])

      const summary = service.getValidationChangesSummary()

      expect(summary.totalEntries).toBe(2)
      expect(summary.totalChanges).toBe(3)
      expect(summary.contentTypes[contentType1]).toBeTruthy()
      expect(summary.contentTypes[contentType2]).toBeTruthy()
      expect(summary.contentTypes[contentType1].totalChanges).toBe(2)
      expect(summary.contentTypes[contentType2].totalChanges).toBe(1)
    })
  })

  describe('formatChangesForFrontend', () => {
    test('should format changes correctly', () => {
      const changes = [
        {
          type: 'truncation',
          field: 'seo.metaTitle',
          originalLength: 100,
          newLength: 60,
          message: 'Meta title was truncated'
        }
      ]

      const result = service.formatChangesForFrontend(changes, 'api::page.page', 'es')

      expect(result.formattedChanges).toHaveLength(1)
      expect(result.formattedChanges[0].displayField).toBe('Seo → Meta Title')
      expect(result.formattedChanges[0].severity).toBe('high')
      expect(result.summary.totalChanges).toBe(1)
      expect(result.summary.byType.truncation).toBe(1)
    })

    test('should handle empty changes array', () => {
      const result = service.formatChangesForFrontend([], 'api::page.page', 'es')

      expect(result.formattedChanges).toHaveLength(0)
      expect(result.summary.totalChanges).toBe(0)
    })

    test('should handle invalid input', () => {
      const result = service.formatChangesForFrontend('not-array', 'api::page.page', 'es')

      expect(result.formattedChanges).toHaveLength(0)
      expect(result.summary.totalChanges).toBe(0)
    })
  })

  describe('utility methods', () => {
    test('getFieldDisplayName should format field paths correctly', () => {
      expect(service.getFieldDisplayName('title', 'api::page.page')).toBe('Title')
      expect(service.getFieldDisplayName('seo.metaTitle', 'api::page.page')).toBe('Seo → Meta Title')
      expect(service.getFieldDisplayName('content.0.title', 'api::page.page')).toBe('Content → 0 → Title')
    })

    test('getChangeSeverity should return correct severity levels', () => {
      expect(service.getChangeSeverity('truncation')).toBe('high')
      expect(service.getChangeSeverity('padding')).toBe('medium')
      expect(service.getChangeSeverity('correction')).toBe('low')
      expect(service.getChangeSeverity('unknown')).toBe('medium')
    })

    test('getChangeDescription should return human-readable descriptions', () => {
      const truncationChange = {
        type: 'truncation',
        originalLength: 100,
        newLength: 60
      }
      expect(service.getChangeDescription(truncationChange)).toContain('shortened from 100 to 60')

      const paddingChange = {
        type: 'padding',
        originalLength: 10,
        newLength: 20
      }
      expect(service.getChangeDescription(paddingChange)).toContain('extended from 10 to 20')

      const customChange = {
        type: 'custom',
        message: 'Custom message'
      }
      expect(service.getChangeDescription(customChange)).toBe('Custom message')
    })
  })
})
