'use strict'

const {
  extractFieldValidationRules,
  getSchemaValidationRules,
  validateFieldValue,
  validateTranslatedContent
} = require('../field-validation')

const setup = function () {
  Object.defineProperty(global, 'strapi', {
    value: {
      components: {
        'shared.seo': {
          attributes: {
            metaTitle: {
              type: 'string',
              required: true,
              maxLength: 70
            },
            metaDescription: {
              type: 'text',
              required: true,
              maxLength: 250
            },
            keywords: {
              type: 'text'
            }
          }
        },
        'shared.meta-social': {
          attributes: {
            title: {
              type: 'string',
              required: true,
              maxLength: 60
            },
            description: {
              type: 'string',
              required: true,
              maxLength: 65
            }
          }
        }
      },
      contentTypes: {
        'api::page.page': {
          attributes: {
            title: {
              type: 'string',
              required: true
            },
            seo: {
              type: 'component',
              component: 'shared.seo',
              repeatable: false
            },
            content: {
              type: 'dynamiczone',
              components: ['shared.meta-social']
            }
          }
        }
      },
      log: {
        error: jest.fn(),
        warn: jest.fn(),
        debug: jest.fn()
      }
    },
    writable: true,
  })
}

afterEach(() => {
  Object.defineProperty(global, 'strapi', {})
})

describe('Field Validation Utility', () => {
  beforeEach(() => {
    setup()
  })

  describe('extractFieldValidationRules', () => {
    test('should extract maxLength rule', () => {
      const fieldSchema = { type: 'string', maxLength: 70 }
      const rules = extractFieldValidationRules(fieldSchema)
      expect(rules).toEqual({ maxLength: 70 })
    })

    test('should extract minLength rule', () => {
      const fieldSchema = { type: 'string', minLength: 5 }
      const rules = extractFieldValidationRules(fieldSchema)
      expect(rules).toEqual({ minLength: 5 })
    })

    test('should extract both min and max length rules', () => {
      const fieldSchema = { type: 'string', minLength: 5, maxLength: 100 }
      const rules = extractFieldValidationRules(fieldSchema)
      expect(rules).toEqual({ minLength: 5, maxLength: 100 })
    })

    test('should return empty object for no rules', () => {
      const fieldSchema = { type: 'string' }
      const rules = extractFieldValidationRules(fieldSchema)
      expect(rules).toEqual({})
    })
  })

  describe('getSchemaValidationRules', () => {
    test('should extract validation rules from page schema including components', () => {
      const schema = strapi.contentTypes['api::page.page']
      const rules = getSchemaValidationRules(schema)

      // Should include SEO component rules
      expect(rules['seo.metaTitle']).toEqual({ maxLength: 70 })
      expect(rules['seo.metaDescription']).toEqual({ maxLength: 250 })

      // Should include dynamic zone component rules with wildcard
      expect(rules['content.*.title']).toEqual({ maxLength: 60 })
      expect(rules['content.*.description']).toEqual({ maxLength: 65 })
    })

    test('should handle empty schema', () => {
      const schema = { attributes: {} }
      const rules = getSchemaValidationRules(schema)
      expect(rules).toEqual({})
    })
  })

  describe('validateFieldValue', () => {
    test('should pass validation for valid string length', () => {
      const rules = { maxLength: 70 }
      const result = validateFieldValue('Short title', rules, 'title')
      expect(result).toBeNull()
    })

    test('should fail validation for string exceeding maxLength', () => {
      const rules = { maxLength: 5 }
      const longString = 'This is a very long string that exceeds the limit'
      const result = validateFieldValue(longString, rules, 'title')

      expect(result).toHaveLength(1)
      expect(result[0]).toMatchObject({
        rule: 'maxLength',
        field: 'title',
        expected: 5,
        actual: longString.length
      })
      expect(result[0].message).toContain('exceeds maximum length')
    })

    test('should fail validation for string below minLength', () => {
      const rules = { minLength: 10 }
      const shortString = 'Short'
      const result = validateFieldValue(shortString, rules, 'title')

      expect(result).toHaveLength(1)
      expect(result[0]).toMatchObject({
        rule: 'minLength',
        field: 'title',
        expected: 10,
        actual: shortString.length
      })
      expect(result[0].message).toContain('below minimum length')
    })

    test('should not validate non-string values', () => {
      const rules = { maxLength: 5 }
      expect(validateFieldValue(123, rules, 'number')).toBeNull()
      expect(validateFieldValue(null, rules, 'null')).toBeNull()
      expect(validateFieldValue(undefined, rules, 'undefined')).toBeNull()
      expect(validateFieldValue({}, rules, 'object')).toBeNull()
    })
  })

  describe('validateTranslatedContent', () => {
    test('should validate page content with SEO component', () => {
      const translatedData = {
        title: 'Valid Page Title',
        seo: {
          metaTitle: 'Valid Meta Title',
          metaDescription: 'Valid meta description that is within the character limit'
        }
      }

      const result = validateTranslatedContent(translatedData, 'api::page.page')
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    test('should fail validation for SEO component with long metaTitle', () => {
      const translatedData = {
        title: 'Valid Page Title',
        seo: {
          metaTitle: 'This is a very long meta title that definitely exceeds the 70 character limit set for SEO meta titles',
          metaDescription: 'Valid meta description'
        }
      }

      const result = validateTranslatedContent(translatedData, 'api::page.page')
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].field).toBe('seo.metaTitle')
      expect(result.errors[0].rule).toBe('maxLength')
    })

    test('should validate dynamic zone content', () => {
      const translatedData = {
        title: 'Valid Page Title',
        content: [
          {
            __component: 'shared.meta-social',
            title: 'Valid social title',
            description: 'Valid social description'
          }
        ]
      }

      const result = validateTranslatedContent(translatedData, 'api::page.page')
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    test('should fail validation for dynamic zone content with long title', () => {
      const translatedData = {
        title: 'Valid Page Title',
        content: [
          {
            __component: 'shared.meta-social',
            title: 'This is a very long social media title that exceeds the 60 character limit',
            description: 'Valid description'
          }
        ]
      }

      const result = validateTranslatedContent(translatedData, 'api::page.page')
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].field).toBe('content.0.title')
      expect(result.errors[0].rule).toBe('maxLength')
    })

    test('should handle multiple validation errors', () => {
      const translatedData = {
        title: 'Valid Page Title',
        seo: {
          metaTitle: 'This is a very long meta title that definitely exceeds the 70 character limit set for SEO meta titles',
          metaDescription: 'This is an extremely long meta description that definitely exceeds the 250 character limit set for SEO meta descriptions. It contains way too much text and should trigger a validation error when processed by the field validation utility function.'
        }
      }

      const result = validateTranslatedContent(translatedData, 'api::page.page')
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(2)

      const titleError = result.errors.find(e => e.field === 'seo.metaTitle')
      const descError = result.errors.find(e => e.field === 'seo.metaDescription')

      expect(titleError).toBeDefined()
      expect(descError).toBeDefined()
      expect(titleError.rule).toBe('maxLength')
      expect(descError.rule).toBe('maxLength')
    })

    test('should handle missing content-type schema', () => {
      const translatedData = { title: 'Test' }
      const result = validateTranslatedContent(translatedData, 'api::nonexistent.nonexistent')

      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(1)
      expect(result.errors[0].rule).toBe('system')
      expect(result.errors[0].message).toContain('Content-type schema not found')
    })
  })
})
