'use strict'

const { cleanData } = require('./clean-data')
const { validateTranslatedContent, autoCorrectTranslatedContent } = require('./field-validation')
const { getService } = require('./get-service')
const lodashHelpers = require('./lodash-helpers')
const { populateAll } = require('./populate-all')
const translatableFields = require('./translatable-fields')
const { translateRelations } = require('./translate-relations')
const { updateUids } = require('./update-uids')

module.exports = {
  cleanData,
  validateTranslatedContent,
  autoCorrectTranslatedContent,
  getService,
  ...lodashHelpers,
  populateAll,
  ...translatableFields,
  translateRelations,
  updateUids,
}
