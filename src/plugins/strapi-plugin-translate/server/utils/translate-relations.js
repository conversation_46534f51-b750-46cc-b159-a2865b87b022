'use strict'

const _ = require('lodash')

async function getRelevantLocalization(contentType, id, locale) {
  const relationContent = await strapi
    .service(contentType)
    .findOne(id, { populate: 'localizations' })
  return relationContent.localizations.filter((l) => l.locale === locale)[0]
}

/**
 * Translate relations by either copying, deleting or using the corresponding locale
 * @param {object} data The data to translate
 * @param {object} schema The schema of the content-type
 * @param {string} targetLocale The target locale (iso-format)
 * @returns The input data with relations either
 *  - copied in the case they can be resued
 *  - deleted if they cannot be reused
 *  - translated if the relation target is localized and the related instance has the targetLocale created
 */
async function translateRelations(data, schema, targetLocale, originalData) {
  const { translateRelations: shouldTranslateRelations } =
    strapi.config.get('plugin.translate')

  const attributesSchema = _.get(schema, 'attributes', [])
  const resultData = _.cloneDeep(data)
  await Promise.all(
    Object.keys(attributesSchema).map(async (attr) => {
      try {

        console.log('attr: ', attr);
        if (attr === 'localizations') {
          return
        }

        const attributeData = _.get(data, attr, undefined);
        const originalattributeData = _.get(originalData, attr, undefined);

        if ((attributeData === null || attributeData === undefined) && !originalattributeData) {
          return
        }

        const attributeSchema = attributesSchema[attr]

        const onTranslate = _.get(
          attributeSchema,
          ['pluginOptions', 'translate', 'translate'],
          'translate'
        );

        if (
          ['relation', 'component', 'dynamiczone'].includes(attributeSchema.type)
        ) {
          switch (onTranslate) {
            case 'copy':
              if (attributeSchema.type === 'relation') {
                resultData[attr] = shouldTranslateRelations
                  ? await translateRelation(
                    attributeData,
                    attributeSchema,
                    targetLocale
                  )
                  : undefined
              } else {
                resultData[attr] = attributeData
              }
              break
            case 'delete':
              resultData[attr] = undefined
              break
            case 'translate':
            default:
              if (attributeSchema.type === 'relation') {
                resultData[attr] = shouldTranslateRelations
                  ? await translateRelation(
                    attributeData,
                    attributeSchema,
                    targetLocale
                  )
                  : undefined
              } else if (attributeSchema.type === 'component') {
                resultData[attr] = await translateComponent(
                  attributeData,
                  attributeSchema,
                  targetLocale
                )
              } else if (attributeSchema.type === 'dynamiczone') {
                resultData[attr] = await Promise.all(
                  attributeData.map((object) =>
                    translateComponent(object, attributeSchema, targetLocale)
                  )
                )
              }
              break
          }
        } else if (attributeSchema.type === 'json' || attr.includes('custom')) {
          resultData[attr] = await translateJsonField(attributeData || originalattributeData, attributeSchema, targetLocale);
        }
      } catch (error) {
        console.trace(`${attr} error:`, error);
      }
    })

  )
  return resultData
}

async function translateComponent(data, componentReference, targetLocale) {
  if (!data) {
    return undefined
  }
  const componentSchema =
    componentReference.type === 'dynamiczone'
      ? strapi.components[data.__component]
      : strapi.components[componentReference.component]
  if (componentReference.repeatable) {
    return Promise.all(
      data.map((value) =>
        translateRelations(value, componentSchema, targetLocale)
      )
    )
  }
  return translateRelations(data, componentSchema, targetLocale)
}

async function translateRelation(attributeData, attributeSchema, targetLocale) {
  const relationSchema = strapi.contentTypes[attributeSchema.target]

  const relationIsLocalized = _.get(
    relationSchema,
    'pluginOptions.i18n.localized',
    false
  )

  const onTranslate = _.get(
    attributeSchema,
    'pluginOptions.translate.translate',
    'translate'
  )

  const relationIsBothWays =
    _.has(attributeSchema, 'inversedBy', false) ||
    _.has(attributeSchema, 'mappedBy', false)

  if (onTranslate === 'delete') {
    return undefined
  }

  if (onTranslate === 'copy') {
    if (relationIsLocalized || relationIsBothWays) {
      return ['oneToMany', 'manyToMany'].includes(attributeSchema.relation)
        ? []
        : undefined
    } else {
      return attributeData
    }
  }

  // If the relation is localized, the relevant localizations from the relation should be selected
  if (relationIsLocalized) {
    // for oneToMany and manyToMany relations there are multiple relations possible, so all of them need to be considered
    if (
      ['oneToMany', 'manyToMany'].includes(attributeSchema.relation) &&
      attributeData?.length > 0
    ) {
      return _.compact(
        await Promise.all(
          attributeData.map(async (prevRelation) =>
            getRelevantLocalization(
              attributeSchema.target,
              prevRelation.id,
              targetLocale
            )
          )
        )
      )
    } else if (
      ['oneToOne', 'manyToOne'].includes(attributeSchema.relation) &&
      attributeData
    ) {
      return getRelevantLocalization(
        attributeSchema.target,
        attributeData.id,
        targetLocale
      )
    }
  } else if (
    relationIsBothWays &&
    ['oneToOne', 'oneToMany'].includes(attributeSchema.relation)
  ) {
    // In this case the relations in other locales or in the referenced relations would be deleted
    // so there is not really a different option than to not include these relations
    return attributeSchema.relation == 'oneToMany' ? [] : undefined
  }
  return attributeData
}

async function translateJsonField(attributeData, attributeSchema, targetLocale) {
  console.log('translateJsonField attributeSchema.type: ', attributeSchema.type);

  if (!attributeData) {
    return attributeData;
  }

  // console.log('translateJsonField attributeData: ', JSON.stringify(attributeData, null, 2));
  // console.log('translateJsonField attributeSchema: ', JSON.stringify(attributeSchema, null, 2));
  const model = attributeSchema.pluginOptions?.customi18n?.model;
  console.log('translateJsonField model: ', model);
  if (model) {
    const customField = attributeSchema.pluginOptions?.customi18n?.field;
    console.log('translateJsonField customField: ', customField);
    let originalId = null;
    let relatedLocalizationFound = null;
    if (customField) {
      if (attributeData[customField]) {
        originalId = attributeData[customField];
      }
    } else {
      originalId = attributeData;
    }

    console.log('translateJsonField originalId: ', originalId);
    if (originalId) {
      const originalElement = await strapi.entityService.findOne(model, originalId, {
        populate: ["localizations"]
      });
      // console.log('translateJsonField originalElement: ', JSON.stringify(originalElement, null, 2));
      relatedLocalizationFound = originalElement?.localizations?.find(({ locale }) => locale === targetLocale);
      // console.log('translateJsonField relatedLocalizationFound: ', JSON.stringify(relatedLocalizationFound, null, 2));
    }

    if (relatedLocalizationFound) {
      if (customField) {
        attributeData[customField] = `${relatedLocalizationFound.id}`;
      } else {
        attributeData = `${relatedLocalizationFound.id}`;
      }
    }
  }

  return attributeData;
}

module.exports = {
  translateRelations,
}
