'use strict'

const _ = require('lodash')
const { truncateText, padText, getTextLength } = require('./text-auto-correction')

/**
 * Extract validation rules from a field schema
 * @param {object} fieldSchema - The field schema definition
 * @returns {object} - Object containing validation rules (maxLength, minLength, etc.)
 */
function extractFieldValidationRules(fieldSchema) {
  const rules = {}
  
  if (fieldSchema.maxLength !== undefined) {
    rules.maxLength = fieldSchema.maxLength
  }
  
  if (fieldSchema.minLength !== undefined) {
    rules.minLength = fieldSchema.minLength
  }
  
  if (fieldSchema.max !== undefined) {
    rules.max = fieldSchema.max
  }
  
  if (fieldSchema.min !== undefined) {
    rules.min = fieldSchema.min
  }
  
  return rules
}

/**
 * Get all validation rules for a content-type schema, including nested components
 * @param {object} schema - The content-type schema
 * @param {string} basePath - Base path for nested fields (used for recursion)
 * @returns {object} - Object mapping field paths to their validation rules
 */
function getSchemaValidationRules(schema, basePath = '') {
  const validationRules = {}
  const attributesSchema = _.get(schema, 'attributes', {})
  
  Object.keys(attributesSchema).forEach((attr) => {
    const fieldSchema = attributesSchema[attr]
    const fieldPath = basePath ? `${basePath}.${attr}` : attr
    
    // Extract direct field validation rules
    const rules = extractFieldValidationRules(fieldSchema)
    if (Object.keys(rules).length > 0) {
      validationRules[fieldPath] = rules
    }
    
    // Handle component fields
    if (fieldSchema.type === 'component') {
      const componentSchema = strapi.components[fieldSchema.component]
      if (componentSchema) {
        const componentRules = getSchemaValidationRules(componentSchema, fieldPath)
        Object.assign(validationRules, componentRules)
      }
    }
    
    // Handle dynamic zone fields
    if (fieldSchema.type === 'dynamiczone') {
      // For dynamic zones, we need to check all possible components
      fieldSchema.components?.forEach((componentName) => {
        const componentSchema = strapi.components[componentName]
        if (componentSchema) {
          // Dynamic zone components are indexed, so we use a pattern like field.0.subfield
          const componentRules = getSchemaValidationRules(componentSchema, `${fieldPath}.*`)
          Object.assign(validationRules, componentRules)
        }
      })
    }
  })
  
  return validationRules
}

/**
 * Auto-correct a single field value to meet validation rules
 * @param {any} value - The field value to correct
 * @param {object} rules - The validation rules for this field
 * @param {string} fieldPath - The path to the field (for logging)
 * @param {object} options - Correction options
 * @returns {object} - {value: corrected value, corrected: boolean, changes: array of changes made}
 */
function autoCorrectFieldValue(value, rules, fieldPath, options = {}) {
  const { enableAutoCorrection = true } = options

  if (!value || typeof value !== 'string' || !enableAutoCorrection) {
    return { value, corrected: false, changes: [] }
  }

  let correctedValue = value
  const changes = []
  const originalLength = getTextLength(value)

  // Extract field type from path for context-aware corrections
  const fieldType = fieldPath.split('.').pop() || 'text'

  // Handle maxLength violations
  if (rules.maxLength !== undefined && getTextLength(correctedValue) > rules.maxLength) {
    const truncationResult = truncateText(correctedValue, rules.maxLength, {
      addEllipsis: true,
      preserveHTML: true,
      preserveMarkdown: true
    })

    if (truncationResult.truncated) {
      correctedValue = truncationResult.text
      changes.push({
        type: 'truncation',
        field: fieldPath,
        originalLength: truncationResult.originalLength,
        newLength: truncationResult.newLength,
        method: truncationResult.method,
        message: `Truncated '${fieldPath}' from ${truncationResult.originalLength} to ${truncationResult.newLength} characters using ${truncationResult.method} boundary`
      })
    }
  }

  // Handle minLength violations
  if (rules.minLength !== undefined && getTextLength(correctedValue) < rules.minLength) {
    const paddingResult = padText(correctedValue, rules.minLength, fieldType, {
      strategy: 'auto',
      preserveHTML: true,
      preserveMarkdown: true
    })

    if (paddingResult.padded) {
      correctedValue = paddingResult.text
      changes.push({
        type: 'padding',
        field: fieldPath,
        originalLength: paddingResult.originalLength,
        newLength: paddingResult.newLength,
        method: paddingResult.method,
        message: `Extended '${fieldPath}' from ${paddingResult.originalLength} to ${paddingResult.newLength} characters using ${paddingResult.method} strategy`
      })
    }
  }

  return {
    value: correctedValue,
    corrected: changes.length > 0,
    changes
  }
}

/**
 * Validate a single field value against its validation rules
 * @param {any} value - The field value to validate
 * @param {object} rules - The validation rules for this field
 * @param {string} fieldPath - The path to the field (for error reporting)
 * @returns {object|null} - Error object if validation fails, null if valid
 */
function validateFieldValue(value, rules, fieldPath) {
  if (!value || typeof value !== 'string') {
    return null // Only validate string fields for length
  }

  const errors = []
  const actualLength = getTextLength(value)

  if (rules.maxLength !== undefined && actualLength > rules.maxLength) {
    errors.push({
      field: fieldPath,
      rule: 'maxLength',
      expected: rules.maxLength,
      actual: actualLength,
      message: `Field '${fieldPath}' exceeds maximum length of ${rules.maxLength} characters (current: ${actualLength})`
    })
  }

  if (rules.minLength !== undefined && actualLength < rules.minLength) {
    errors.push({
      field: fieldPath,
      rule: 'minLength',
      expected: rules.minLength,
      actual: actualLength,
      message: `Field '${fieldPath}' is below minimum length of ${rules.minLength} characters (current: ${actualLength})`
    })
  }

  return errors.length > 0 ? errors : null
}

/**
 * Auto-correct translated data against schema validation rules
 * @param {object} data - The translated data to correct
 * @param {object} validationRules - The validation rules extracted from schema
 * @param {string} basePath - Base path for nested correction (used for recursion)
 * @param {object} options - Correction options
 * @returns {object} - {data: corrected data, corrected: boolean, changes: array of changes made}
 */
function autoCorrectDataAgainstRules(data, validationRules, basePath = '', options = {}) {
  const correctedData = _.cloneDeep(data)
  const allChanges = []
  let anythingCorrected = false

  Object.keys(correctedData).forEach((key) => {
    const fieldPath = basePath ? `${basePath}.${key}` : key
    const value = correctedData[key]

    // Check if we have validation rules for this exact field
    if (validationRules[fieldPath]) {
      const correctionResult = autoCorrectFieldValue(value, validationRules[fieldPath], fieldPath, options)
      if (correctionResult.corrected) {
        correctedData[key] = correctionResult.value
        allChanges.push(...correctionResult.changes)
        anythingCorrected = true
      }
    }

    // Handle nested objects (components)
    if (_.isPlainObject(value) && !Array.isArray(value)) {
      const nestedResult = autoCorrectDataAgainstRules(value, validationRules, fieldPath, options)
      if (nestedResult.corrected) {
        correctedData[key] = nestedResult.data
        allChanges.push(...nestedResult.changes)
        anythingCorrected = true
      }
    }

    // Handle arrays (dynamic zones or repeatable components)
    if (Array.isArray(value)) {
      value.forEach((item, index) => {
        if (_.isPlainObject(item)) {
          const arrayItemPath = `${fieldPath}.${index}`
          const arrayResult = autoCorrectDataAgainstRules(item, validationRules, arrayItemPath, options)
          if (arrayResult.corrected) {
            correctedData[key][index] = arrayResult.data
            allChanges.push(...arrayResult.changes)
            anythingCorrected = true
          }

          // Also check for wildcard rules (dynamic zone patterns)
          const wildcardPath = `${fieldPath}.*`
          Object.keys(validationRules).forEach((rulePath) => {
            if (rulePath.startsWith(wildcardPath)) {
              // Extract the field name after the wildcard (e.g., "title" from "content.*.title")
              const fieldNameAfterWildcard = rulePath.substring(wildcardPath.length + 1) // +1 for the dot after *
              const actualFieldPath = `${arrayItemPath}.${fieldNameAfterWildcard}`
              const fieldValue = _.get(item, fieldNameAfterWildcard)
              if (fieldValue !== undefined) {
                const correctionResult = autoCorrectFieldValue(fieldValue, validationRules[rulePath], actualFieldPath, options)
                if (correctionResult.corrected) {
                  _.set(correctedData[key][index], fieldNameAfterWildcard, correctionResult.value)
                  allChanges.push(...correctionResult.changes)
                  anythingCorrected = true
                }
              }
            }
          })
        }
      })
    }
  })

  return {
    data: correctedData,
    corrected: anythingCorrected,
    changes: allChanges
  }
}

/**
 * Validate translated data against schema validation rules
 * @param {object} data - The translated data to validate
 * @param {object} validationRules - The validation rules extracted from schema
 * @param {string} basePath - Base path for nested validation (used for recursion)
 * @returns {array} - Array of validation errors
 */
function validateDataAgainstRules(data, validationRules, basePath = '') {
  const errors = []

  Object.keys(data).forEach((key) => {
    const fieldPath = basePath ? `${basePath}.${key}` : key
    const value = data[key]

    // Check if we have validation rules for this exact field
    if (validationRules[fieldPath]) {
      const fieldErrors = validateFieldValue(value, validationRules[fieldPath], fieldPath)
      if (fieldErrors) {
        errors.push(...fieldErrors)
      }
    }

    // Handle nested objects (components)
    if (_.isPlainObject(value) && !Array.isArray(value)) {
      const nestedErrors = validateDataAgainstRules(value, validationRules, fieldPath)
      errors.push(...nestedErrors)
    }

    // Handle arrays (dynamic zones or repeatable components)
    if (Array.isArray(value)) {
      value.forEach((item, index) => {
        if (_.isPlainObject(item)) {
          const arrayItemPath = `${fieldPath}.${index}`
          const arrayErrors = validateDataAgainstRules(item, validationRules, arrayItemPath)
          errors.push(...arrayErrors)

          // Also check for wildcard rules (dynamic zone patterns)
          const wildcardPath = `${fieldPath}.*`
          Object.keys(validationRules).forEach((rulePath) => {
            if (rulePath.startsWith(wildcardPath)) {
              // Extract the field name after the wildcard (e.g., "title" from "content.*.title")
              const fieldNameAfterWildcard = rulePath.substring(wildcardPath.length + 1) // +1 for the dot after *
              const actualFieldPath = `${arrayItemPath}.${fieldNameAfterWildcard}`
              const fieldValue = _.get(item, fieldNameAfterWildcard)
              if (fieldValue !== undefined) {
                const fieldErrors = validateFieldValue(fieldValue, validationRules[rulePath], actualFieldPath)
                if (fieldErrors) {
                  errors.push(...fieldErrors)
                }
              }
            }
          })
        }
      })
    }
  })

  return errors
}

/**
 * Main auto-correction function for translated content
 * @param {object} translatedData - The translated data to auto-correct
 * @param {string} contentType - The content-type UID
 * @param {object} options - Auto-correction options
 * @returns {object} - Auto-correction result with corrected data and changes
 */
function autoCorrectTranslatedContent(translatedData, contentType, options = {}) {
  try {
    const schema = strapi.contentTypes[contentType]
    if (!schema) {
      throw new Error(`Content-type schema not found: ${contentType}`)
    }

    const validationRules = getSchemaValidationRules(schema)
    const correctionResult = autoCorrectDataAgainstRules(translatedData, validationRules, '', options)

    // Log changes if any were made
    if (correctionResult.corrected && correctionResult.changes.length > 0) {
      strapi.log.info(`Auto-corrected ${correctionResult.changes.length} field(s) in ${contentType}:`)
      correctionResult.changes.forEach(change => {
        strapi.log.info(`  - ${change.message}`)
      })
    }

    return {
      data: correctionResult.data,
      corrected: correctionResult.corrected,
      changes: correctionResult.changes,
      validationRules // Include for debugging
    }
  } catch (error) {
    strapi.log.error('Error during field auto-correction:', error)
    return {
      data: translatedData, // Return original data on error
      corrected: false,
      changes: [],
      error: error.message,
      validationRules: {}
    }
  }
}

/**
 * Main validation function for translated content
 * @param {object} translatedData - The translated data to validate
 * @param {string} contentType - The content-type UID
 * @param {object} options - Validation options
 * @returns {object} - Validation result with isValid flag and errors array
 */
function validateTranslatedContent(translatedData, contentType, options = {}) {
  const { autoCorrect = false } = options

  try {
    const schema = strapi.contentTypes[contentType]
    if (!schema) {
      throw new Error(`Content-type schema not found: ${contentType}`)
    }

    const validationRules = getSchemaValidationRules(schema)

    // If auto-correction is enabled, try to fix issues first
    if (autoCorrect) {
      const correctionResult = autoCorrectDataAgainstRules(translatedData, validationRules, '', options)

      // Validate the corrected data
      const errors = validateDataAgainstRules(correctionResult.data, validationRules)

      return {
        isValid: errors.length === 0,
        errors,
        data: correctionResult.data,
        corrected: correctionResult.corrected,
        changes: correctionResult.changes,
        validationRules
      }
    } else {
      // Standard validation without auto-correction
      const errors = validateDataAgainstRules(translatedData, validationRules)

      return {
        isValid: errors.length === 0,
        errors,
        data: translatedData,
        corrected: false,
        changes: [],
        validationRules
      }
    }
  } catch (error) {
    strapi.log.error('Error during field validation:', error)
    return {
      isValid: false,
      errors: [{
        field: 'validation',
        rule: 'system',
        message: `Validation system error: ${error.message}`
      }],
      data: translatedData,
      corrected: false,
      changes: [],
      validationRules: {}
    }
  }
}

module.exports = {
  validateTranslatedContent,
  autoCorrectTranslatedContent,
  getSchemaValidationRules,
  validateDataAgainstRules,
  autoCorrectDataAgainstRules,
  validateFieldValue,
  autoCorrectFieldValue,
  extractFieldValidationRules
}
