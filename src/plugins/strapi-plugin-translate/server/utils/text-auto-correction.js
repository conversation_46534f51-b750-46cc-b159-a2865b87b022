'use strict'

/**
 * Utility for automatically correcting text length violations
 * Handles both truncation (maxLength) and padding (minLength) with smart boundary detection
 */

/**
 * Detect if text contains HTML tags
 * @param {string} text - Text to check
 * @returns {boolean} - True if HTML is detected
 */
function containsHTML(text) {
  return /<[^>]*>/g.test(text)
}

/**
 * Detect if text contains Markdown formatting
 * @param {string} text - Text to check
 * @returns {boolean} - True if Markdown is detected
 */
function containsMarkdown(text) {
  // Basic markdown patterns: **bold**, *italic*, [links](url), # headers, etc.
  const markdownPatterns = [
    /\*\*[^*]+\*\*/g,  // **bold**
    /\*[^*]+\*/g,      // *italic*
    /\[[^\]]+\]\([^)]+\)/g,  // [link](url)
    /^#{1,6}\s/gm,     // # headers
    /`[^`]+`/g,        // `code`
    /^\s*[-*+]\s/gm,   // - list items
    /^\s*\d+\.\s/gm    // 1. numbered lists
  ]
  
  return markdownPatterns.some(pattern => pattern.test(text))
}

/**
 * Strip HTML tags from text while preserving content
 * @param {string} text - Text with HTML
 * @returns {string} - Text without HTML tags
 */
function stripHTML(text) {
  return text.replace(/<[^>]*>/g, '')
}

/**
 * Get text length without HTML tags for accurate length calculation
 * @param {string} text - Text to measure
 * @returns {number} - Length without HTML tags
 */
function getTextLength(text) {
  if (containsHTML(text)) {
    return stripHTML(text).length
  }
  return text.length
}

/**
 * Find the best truncation point in text
 * @param {string} text - Text to truncate
 * @param {number} maxLength - Maximum allowed length
 * @returns {object} - {position: number, type: 'sentence'|'word'|'character'}
 */
function findTruncationPoint(text, maxLength) {
  const plainText = containsHTML(text) ? stripHTML(text) : text
  
  if (plainText.length <= maxLength) {
    return { position: text.length, type: 'none' }
  }
  
  // Try to find sentence boundary (. ! ?)
  const sentenceEnds = /[.!?]\s*/g
  let match
  let lastSentenceEnd = 0

  while ((match = sentenceEnds.exec(plainText)) !== null) {
    const endPosition = match.index + 1 // Include the punctuation mark
    if (endPosition <= maxLength) {
      lastSentenceEnd = endPosition
    } else {
      break
    }
  }

  if (lastSentenceEnd > 0 && lastSentenceEnd >= maxLength * 0.5) { // Don't cut too much
    return { position: mapPlainToOriginal(text, lastSentenceEnd), type: 'sentence' }
  }
  
  // Try to find word boundary
  const wordBoundary = plainText.lastIndexOf(' ', maxLength)
  if (wordBoundary > maxLength * 0.7) { // Don't cut too much
    return { position: mapPlainToOriginal(text, wordBoundary), type: 'word' }
  }
  
  // Character boundary as fallback
  return { position: mapPlainToOriginal(text, maxLength), type: 'character' }
}

/**
 * Map position in plain text back to original text with HTML
 * @param {string} originalText - Original text with HTML
 * @param {number} plainPosition - Position in plain text
 * @returns {number} - Position in original text
 */
function mapPlainToOriginal(originalText, plainPosition) {
  if (!containsHTML(originalText)) {
    return plainPosition
  }
  
  let originalPos = 0
  let plainPos = 0
  let inTag = false
  
  while (originalPos < originalText.length && plainPos < plainPosition) {
    const char = originalText[originalPos]
    
    if (char === '<') {
      inTag = true
    } else if (char === '>') {
      inTag = false
    } else if (!inTag) {
      plainPos++
    }
    
    originalPos++
  }
  
  return originalPos
}

/**
 * Ensure HTML tags are properly closed after truncation
 * @param {string} text - Truncated text that may have unclosed tags
 * @returns {string} - Text with properly closed HTML tags
 */
function fixUnclosedHTMLTags(text) {
  if (!containsHTML(text)) {
    return text
  }

  const openTags = []
  const tagRegex = /<\/?([a-zA-Z][a-zA-Z0-9]*)[^>]*>/g
  let match

  // Find all tags and track which ones are open
  while ((match = tagRegex.exec(text)) !== null) {
    const tagName = match[1].toLowerCase()
    const isClosing = match[0].startsWith('</')
    const isSelfClosing = match[0].endsWith('/>') || ['img', 'br', 'hr', 'input', 'meta', 'link'].includes(tagName)

    if (isSelfClosing) {
      continue
    }

    if (isClosing) {
      // Remove the last occurrence of this tag from openTags
      const index = openTags.lastIndexOf(tagName)
      if (index !== -1) {
        openTags.splice(index, 1)
      }
    } else {
      openTags.push(tagName)
    }
  }

  // Close any remaining open tags
  let result = text
  for (let i = openTags.length - 1; i >= 0; i--) {
    result += `</${openTags[i]}>`
  }

  return result
}

/**
 * Intelligently truncate text to fit within maxLength
 * @param {string} text - Text to truncate
 * @param {number} maxLength - Maximum allowed length
 * @param {object} options - Truncation options
 * @returns {object} - {text: string, truncated: boolean, originalLength: number, newLength: number, method: string}
 */
function truncateText(text, maxLength, options = {}) {
  const { addEllipsis = true, preserveHTML = true, preserveMarkdown = true } = options

  if (!text || typeof text !== 'string') {
    return { text: text || '', truncated: false, originalLength: 0, newLength: 0, method: 'none' }
  }

  const originalLength = getTextLength(text)

  if (originalLength <= maxLength) {
    return { text, truncated: false, originalLength, newLength: originalLength, method: 'none' }
  }

  // Adjust maxLength if we need to add ellipsis
  const ellipsis = '...'
  const adjustedMaxLength = addEllipsis ? Math.max(1, maxLength - ellipsis.length) : maxLength

  const truncationPoint = findTruncationPoint(text, adjustedMaxLength)
  let truncatedText = text.substring(0, truncationPoint.position).trim()

  // Fix unclosed HTML tags if preserving HTML
  if (preserveHTML && containsHTML(truncatedText)) {
    truncatedText = fixUnclosedHTMLTags(truncatedText)
  }

  // Handle Markdown formatting preservation
  if (preserveMarkdown && containsMarkdown(truncatedText)) {
    // Ensure we don't break markdown links or formatting
    truncatedText = fixBrokenMarkdown(truncatedText)
  }

  // Add ellipsis if truncated and requested
  if (addEllipsis && truncationPoint.type !== 'none') {
    if (containsHTML(truncatedText)) {
      // For HTML content, add ellipsis before closing tags
      const lastClosingTag = truncatedText.lastIndexOf('</')
      if (lastClosingTag > 0) {
        const beforeClosing = truncatedText.substring(0, lastClosingTag)
        const closingTags = truncatedText.substring(lastClosingTag)
        truncatedText = beforeClosing + ellipsis + closingTags
      } else {
        truncatedText += ellipsis
      }
    } else {
      truncatedText += ellipsis
    }
  }

  const newLength = getTextLength(truncatedText)

  return {
    text: truncatedText,
    truncated: true,
    originalLength,
    newLength,
    method: truncationPoint.type
  }
}

/**
 * Fix broken Markdown formatting after truncation
 * @param {string} text - Text that may have broken Markdown
 * @returns {string} - Text with fixed Markdown formatting
 */
function fixBrokenMarkdown(text) {
  // Remove incomplete markdown links [text](
  text = text.replace(/\[[^\]]*\]\([^)]*$/, '')

  // Remove incomplete bold/italic formatting
  text = text.replace(/\*+[^*]*$/, '')

  // Remove incomplete code blocks
  text = text.replace(/`[^`]*$/, '')

  // Remove incomplete headers at the end
  text = text.replace(/^#{1,6}\s[^#]*$/, '')

  return text.trim()
}

/**
 * Extract keywords from text for intelligent padding
 * @param {string} text - Text to extract keywords from
 * @returns {array} - Array of potential keywords
 */
function extractKeywords(text) {
  if (!text) return []

  // Remove HTML tags and get plain text
  const plainText = stripHTML(text).toLowerCase()

  // Split into words and filter out common stop words
  const stopWords = new Set([
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
    'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had', 'do', 'does', 'did',
    'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those'
  ])

  const words = plainText
    .replace(/[^\w\s]/g, ' ')
    .split(/\s+/)
    .filter(word => word.length > 2 && !stopWords.has(word))
    .slice(0, 5) // Take first 5 meaningful words

  return words
}

/**
 * Generate contextually appropriate padding text
 * @param {string} originalText - Original text to extend
 * @param {string} fieldType - Type of field (e.g., 'metaTitle', 'metaDescription')
 * @param {number} targetLength - Target length to reach
 * @returns {string} - Padding text to add
 */
function generatePaddingText(originalText, fieldType, targetLength) {
  const currentLength = getTextLength(originalText)
  const neededLength = targetLength - currentLength

  if (neededLength <= 0) {
    return ''
  }

  // Extract keywords for context-aware padding
  const keywords = extractKeywords(originalText)
  const hasKeywords = keywords.length > 0

  // Context-aware padding based on field type
  const paddingStrategies = {
    metaTitle: [
      hasKeywords ? ` - ${keywords[0]} Guide` : ' - Complete Guide',
      hasKeywords ? ` | ${keywords[0]} Tips` : ' | Expert Tips',
      ' - Learn More',
      ' | Best Practices',
      ' - Everything You Need to Know',
      ' - Professional Services',
      ' | Ultimate Resource'
    ],
    metaDescription: [
      hasKeywords ? ` Discover comprehensive information about ${keywords.slice(0, 2).join(' and ')}.` : ' Discover more about this topic and explore related information.',
      hasKeywords ? ` Learn everything about ${keywords[0]} with our detailed guide.` : ' Learn everything you need to know with our comprehensive guide.',
      hasKeywords ? ` Get expert insights on ${keywords[0]} and practical tips.` : ' Get expert insights and practical tips for better results.',
      ' Find out more details and helpful resources on this subject.',
      ' Explore additional information and related topics.',
      ' Access comprehensive resources and expert guidance.',
      ' Get detailed information and professional advice.'
    ],
    title: [
      hasKeywords ? ` - ${keywords[0]} Guide` : ' - Complete Guide',
      ' - Learn More',
      ' | Expert Tips',
      ' - Everything You Need',
      ' | Professional Service',
      ' - Ultimate Resource'
    ],
    description: [
      hasKeywords ? ` Learn more about ${keywords[0]}.` : ' Learn more about this topic.',
      ' Discover additional information.',
      ' Find out more details.',
      ' Explore related content.',
      ' Get comprehensive information.',
      ' Access detailed resources.'
    ]
  }

  // Get appropriate padding options for field type
  const fieldTypeLower = fieldType.toLowerCase()
  let paddingOptions = paddingStrategies.description // default

  for (const [key, options] of Object.entries(paddingStrategies)) {
    if (fieldTypeLower.includes(key.toLowerCase())) {
      paddingOptions = options
      break
    }
  }

  // Try to find padding that fits
  for (const padding of paddingOptions) {
    if (padding.length <= neededLength) {
      return padding
    }
  }

  // If no predefined padding fits, create custom padding
  if (neededLength >= 20 && hasKeywords) {
    return ` Learn more about ${keywords[0]}.`
  } else if (neededLength >= 15) {
    return ' Learn more here.'
  } else if (neededLength >= 10) {
    return ' More info.'
  } else if (neededLength >= 5) {
    return ' More.'
  } else {
    return '.'.repeat(neededLength)
  }
}

/**
 * Intelligently expand existing content
 * @param {string} text - Original text
 * @param {number} targetLength - Target length
 * @returns {string} - Expanded text
 */
function expandContent(text, targetLength) {
  const currentLength = getTextLength(text)
  const neededLength = targetLength - currentLength

  if (neededLength <= 0) {
    return text
  }

  // Try to expand by adding descriptive words
  const expansions = [
    { pattern: /\b(good|great|nice|cool)\b/gi, replacements: ['excellent', 'outstanding', 'remarkable', 'exceptional'] },
    { pattern: /\b(big|large)\b/gi, replacements: ['comprehensive', 'extensive', 'substantial'] },
    { pattern: /\b(small|little)\b/gi, replacements: ['compact', 'concise', 'focused'] },
    { pattern: /\b(fast|quick)\b/gi, replacements: ['rapid', 'efficient', 'streamlined'] },
    { pattern: /\b(easy|simple)\b/gi, replacements: ['straightforward', 'user-friendly', 'accessible'] }
  ]

  let expandedText = text

  for (const { pattern, replacements } of expansions) {
    const matches = expandedText.match(pattern)
    if (matches && getTextLength(expandedText) < targetLength) {
      const randomReplacement = replacements[Math.floor(Math.random() * replacements.length)]
      expandedText = expandedText.replace(pattern, randomReplacement)

      if (getTextLength(expandedText) >= targetLength) {
        break
      }
    }
  }

  return expandedText
}

/**
 * Intelligently pad text to meet minLength requirement
 * @param {string} text - Text to pad
 * @param {number} minLength - Minimum required length
 * @param {string} fieldType - Type of field for context-aware padding
 * @param {object} options - Padding options
 * @returns {object} - {text: string, padded: boolean, originalLength: number, newLength: number, method: string}
 */
function padText(text, minLength, fieldType = 'text', options = {}) {
  const { strategy = 'auto', preserveHTML = true, preserveMarkdown = true } = options

  if (!text || typeof text !== 'string') {
    text = ''
  }

  const originalLength = getTextLength(text)

  if (originalLength >= minLength) {
    return { text, padded: false, originalLength, newLength: originalLength, method: 'none' }
  }

  let paddedText = text
  let method = 'contextual'

  // Auto strategy: try expansion first, then contextual padding
  if (strategy === 'auto') {
    // First try to expand existing content
    const expandedText = expandContent(text, minLength)
    if (getTextLength(expandedText) >= minLength) {
      paddedText = expandedText
      method = 'expansion'
    } else {
      // If expansion isn't enough, add contextual padding
      const padding = generatePaddingText(expandedText, fieldType, minLength)
      paddedText = expandedText + padding
      method = 'expansion+contextual'
    }
  } else if (strategy === 'contextual') {
    const padding = generatePaddingText(text, fieldType, minLength)
    paddedText = text + padding
    method = 'contextual'
  } else if (strategy === 'expansion') {
    paddedText = expandContent(text, minLength)
    method = 'expansion'

    // If expansion alone isn't enough, fall back to contextual
    if (getTextLength(paddedText) < minLength) {
      const padding = generatePaddingText(paddedText, fieldType, minLength)
      paddedText = paddedText + padding
      method = 'expansion+contextual'
    }
  } else if (strategy === 'repeat') {
    // Repeat the original text intelligently
    const needed = minLength - originalLength
    if (text.length > 0) {
      const repetitions = Math.ceil(needed / text.length)
      const repeated = (' ' + text).repeat(repetitions)
      paddedText = text + repeated.substring(0, needed)
      method = 'repeat'
    }
  } else if (strategy === 'ellipsis') {
    // Simple padding with dots
    const needed = minLength - originalLength
    paddedText = text + '.'.repeat(needed)
    method = 'ellipsis'
  }

  // If we still haven't reached the minimum length, add simple padding
  if (getTextLength(paddedText) < minLength) {
    const stillNeeded = minLength - getTextLength(paddedText)
    paddedText += '.'.repeat(stillNeeded)
    method += '+dots'
  }

  // Ensure we don't exceed the target too much (within 20% tolerance)
  const maxAllowed = Math.floor(minLength * 1.2)
  if (getTextLength(paddedText) > maxAllowed) {
    // Truncate if we went too far
    const truncationResult = truncateText(paddedText, maxAllowed, { addEllipsis: false, preserveHTML, preserveMarkdown })
    paddedText = truncationResult.text
    method += '+truncated'
  }

  const newLength = getTextLength(paddedText)

  return {
    text: paddedText,
    padded: true,
    originalLength,
    newLength,
    method
  }
}

module.exports = {
  truncateText,
  padText,
  getTextLength,
  containsHTML,
  containsMarkdown,
  stripHTML,
  findTruncationPoint,
  generatePaddingText,
  extractKeywords,
  expandContent,
  fixUnclosedHTMLTags,
  fixBrokenMarkdown
}
