'use strict'

const OpenAI = require('openai');
const Bottleneck = require('bottleneck');

const isEditorJs = (text) => {
  try {
    const parsed = JSON.parse(text);
    return parsed.blocks && Array.isArray(parsed.blocks) && parsed.version;
  } catch (error) {
    return false;
  }
};

// Helper function to check if text contains HTML tags
const containsHTML = (text) => {
  const htmlRegex = /<[^>]*>/;
  return htmlRegex.test(text);
};

// Helper function to split text by paragraphs (HTML)
const splitByParagraphs = (text) => {
  // Split by common paragraph separators in HTML
  const paragraphRegex = /(<\/p>|<br\s*\/?>|<\/div>|<\/h[1-6]>)/gi;
  return text.split(paragraphRegex).filter(chunk => chunk.trim().length > 0);
};

// Helper function to split text by line breaks or punctuation
const splitByLineBreaksOrPunctuation = (text) => {
  // First try to split by line breaks
  let chunks = text.split(/\n+/).filter(chunk => chunk.trim().length > 0);

  // If chunks are still too large, split by sentences (periods, exclamation, question marks)
  const maxChunkSize = 2000; // Adjust this value as needed
  let finalChunks = [];

  for (let chunk of chunks) {
    if (chunk.length <= maxChunkSize) {
      finalChunks.push(chunk);
    } else {
      // Split by sentences
      const sentences = chunk.split(/([.!?]+\s+)/).filter(s => s.trim().length > 0);
      let currentChunk = '';

      for (let sentence of sentences) {
        if ((currentChunk + sentence).length <= maxChunkSize) {
          currentChunk += sentence;
        } else {
          if (currentChunk.trim()) {
            finalChunks.push(currentChunk.trim());
          }
          currentChunk = sentence;
        }
      }

      if (currentChunk.trim()) {
        finalChunks.push(currentChunk.trim());
      }
    }
  }

  return finalChunks;
};

// Helper function to split large text into manageable chunks
const splitLargeText = (text, maxSize = 2000) => {
  if (typeof text !== 'string' || text.length <= maxSize) {
    return [text];
  }

  if (containsHTML(text)) {
    return splitByParagraphs(text);
  } else {
    return splitByLineBreaksOrPunctuation(text);
  }
};

// Helper function to process text array and split large texts
const processTextArray = (textArray) => {
  const processedTexts = [];
  const indexMap = []; // To keep track of original indices and chunk counts

  textArray.forEach((text, originalIndex) => {
    if (typeof text === 'string') {
      const chunks = splitLargeText(text);
      chunks.forEach((chunk, chunkIndex) => {
        processedTexts.push(chunk);
        indexMap.push({
          originalIndex,
          chunkIndex,
          totalChunks: chunks.length
        });
      });
    } else {
      processedTexts.push(text);
      indexMap.push({
        originalIndex,
        chunkIndex: 0,
        totalChunks: 1
      });
    }
  });

  return { processedTexts, indexMap };
};

// Helper function to reconstruct original array from translated chunks
const reconstructFromChunks = (translatedChunks, indexMap) => {
  const result = [];
  const chunkGroups = {};

  // Group chunks by original index
  translatedChunks.forEach((chunk, i) => {
    const mapEntry = indexMap[i];
    if (!chunkGroups[mapEntry.originalIndex]) {
      chunkGroups[mapEntry.originalIndex] = [];
    }
    chunkGroups[mapEntry.originalIndex][mapEntry.chunkIndex] = chunk;
  });

  // Reconstruct original array
  Object.keys(chunkGroups).sort((a, b) => parseInt(a) - parseInt(b)).forEach(originalIndex => {
    const chunks = chunkGroups[originalIndex];
    if (chunks.length === 1) {
      result[originalIndex] = chunks[0];
    } else {
      // Join chunks back together
      result[originalIndex] = chunks.join('');
    }
  });

  return result;
};

class ChatGptTranslator {
  _openAiClient;
  _options;

  constructor(_options) {
    this._options = _options;
  }

  _getOpenAiClient() {
    if (!this._openAiClient) {
      this._openAiClient = new OpenAI({
        apiKey: this._options.apiKey,
      });
    }
    return this._openAiClient;
  }

  async translate(options) {
    try {
      console.log('starting new openai request', new Date().toLocaleTimeString());
      // Check if the model supports json_object response format
      const supportsJsonObject = [
        'gpt-4-1106-preview',
        'gpt-4-0125-preview',
        'gpt-4-turbo-preview',
        'gpt-3.5-turbo-1106',
        'gpt-4o',
        'gpt-4o-mini'
      ].includes(this._options.model);

      const requestOptions = {
        model: this._options.model,
        messages: options.messages,
        max_tokens: options.maxTokens,
        temperature: 0.2,
        top_p: 1,
        frequency_penalty: 0,
        presence_penalty: 0,
      };

      // Only add response_format for models that support it
      if (supportsJsonObject) {
        requestOptions.response_format = { type: 'json_object' };
      }

      const response = await this._getOpenAiClient().chat.completions.create(requestOptions);

      const choices = response.choices;

      // console.log('choices', choices);

      if (choices && choices.length > 0) {
        return choices[0].message?.content;
      }
      throw new Error('No result received');
    } catch (error) {
      console.trace('openai translate error:', error);

      const status = error?.response?.status;

      switch (status) {
        case 429:
          throw new Error('Too many requests');
        case 400:
          throw new Error('Bad request');
        default:
          throw new Error(`translate(): ${JSON.stringify(error)}`);
      }
    }
  }

  async usage() {
    return {
      count: 1,
      limit: 4096 * 4,
    };
  }
}

const createTranslateClient = ({ apiKey, model }) => {
  return new ChatGptTranslator({ apiKey, model });
};

class ProviderOptions {
  apiKey;
  model;
  localeMap;
  maxTokens;
  generalPrompt;

  constructor({ apiKey, model, localeMap, maxTokens, generalPrompt }) {
    if (!apiKey) throw new Error(`apiKey is not defined`);
    if (!model) throw new Error(`model is not defined`);
    this.localeMap = localeMap || {};
    this.maxTokens = maxTokens || 4096;
    this.generalPrompt = generalPrompt;

    this.apiKey = apiKey;
    this.model = model;
  }
}

// Recursively extract all string values from an object, preserving their paths
function extractStringValues(obj, path = []) {
  let result = [];
  if (typeof obj === 'string') {
    result.push({ path, value: obj });
  } else if (Array.isArray(obj)) {
    obj.forEach((item, idx) => {
      result = result.concat(extractStringValues(item, path.concat(idx)));
    });
  } else if (obj && typeof obj === 'object') {
    Object.entries(obj).forEach(([key, val]) => {
      result = result.concat(extractStringValues(val, path.concat(key)));
    });
  }
  return result;
}

// Recursively set translated string values back into the object
function setStringValues(obj, stringValues) {
  for (const { path, value } of stringValues) {
    let target = obj;
    for (let i = 0; i < path.length - 1; i++) {
      target = target[path[i]];
    }
    target[path[path.length - 1]] = value;
  }
  return obj;
}

module.exports = {
  provider: 'openai',
  name: 'OpenAI',

  init({ apiKey, model, localeMap, maxTokens, generalPrompt } = {}) {
    const options = new ProviderOptions({
      apiKey: apiKey || process.env.OPENAI_API_TOKEN,
      model: model || process.env.OPENAI_MODEL || 'gpt-3.5-turbo',
      maxTokens: Number(maxTokens) || Number(process.env.OPENAI_MAX_TOKENS) || 4096,
      generalPrompt: generalPrompt || process.env.OPENAI_GENERAL_PROMPT || '',
      localeMap,
    });
    const client = createTranslateClient(options);

    const limiter = new Bottleneck({
      maxConcurrent: 10,
    });

    return {
      async translate({
        text,
        priority,
        sourceLocale,
        targetLocale,
        format,
      }) {
        if (!text) {
          return '';
        }

        if (!sourceLocale) {
          throw new Error('source locale must be defined');
        }

        if (!targetLocale) {
          throw new Error('target locale must be defined');
        }

        const sLocale = sourceLocale;
        const tLocale = targetLocale;

        const promptMap = {
          plain: `Translate ONLY the string values in this JSON object from ${sLocale} to ${tLocale}. DO NOT translate the keys. Return the object with the original keys and translated values. If a value is not a string, leave it unchanged.`,
          editorjs: `Translate the key content editorjs JSON object from ${sLocale} to ${tLocale}, only translating text fields: for "paragraph" it's "text", for "header" it's "text", for "list" it's "items", for "checklist" it's "items", for "quote" it's "text" and "caption", for "table" it's "content", for "image" it's "caption", and for "link_tool" it's "meta.title" and "meta.description". It is important to preserve the structure and leave all other fields untouched.`,
        };

        if (options.generalPrompt) {
          promptMap.plain += `\n\n${options.generalPrompt}`;
          promptMap.editorjs += `\n\n${options.generalPrompt}`;
        }

        // Detect if text is a plain object (not EditorJS, not array, not string)
        let isPlainObject = (
          typeof text === 'object' &&
          text !== null &&
          !Array.isArray(text) &&
          !isEditorJs(JSON.stringify(text))
        );

        if (isPlainObject) {
          // 1. Extract all string values and their paths
          const stringValues = extractStringValues(text);
          if (stringValues.length === 0) {
            return text; // nothing to translate
          }
          // 2. Translate all string values as an array
          const valuesToTranslate = stringValues.map(({ value }) => value);

          // 3. Call OpenAI to translate the array of strings
          // Use the same translate method recursively, but with array of strings
          const translatedValues = await this.translate({
            text: valuesToTranslate,
            sourceLocale,
            targetLocale,
            format,
          });

          // 4. Rebuild the object with translated values
          const translatedStringValues = stringValues.map((item, idx) => ({
            path: item.path,
            value: Array.isArray(translatedValues) ? translatedValues[idx] : translatedValues,
          }));

          // Use structuredClone if available, otherwise fallback to JSON deep clone
          const clone = typeof structuredClone === 'function' ? structuredClone(text) : JSON.parse(JSON.stringify(text));
          const translatedObj = setStringValues(clone, translatedStringValues);
          return translatedObj;
        }

        const textArray = Array.isArray(text) ? text : [text];

        // Process text array to split large texts into chunks
        const { processedTexts, indexMap } = processTextArray(textArray);

        const formattedTextArray = processedTexts
          .map((t) => {
            try {
              if (isEditorJs(t)) {
                const editorjs = JSON.parse(t);
                return { type: 'editorjs', content: editorjs };
              } else {
                return { type: 'plain', content: t };
              }
            } catch (error) {
              return error;
            }
          })
          .filter((t) => t !== undefined);

        const messagesArray = formattedTextArray.map((t) => {
          return [
            {
              role: 'system',
              content: promptMap[t.type],
            },
            { role: 'user', content: JSON.stringify(t) },
          ];
        });

        const result = await Promise.all(
          messagesArray.map((messages) =>
            limiter.schedule(() => {
              return client.translate({
                messages,
                maxTokens: options.maxTokens,
              });
            }),
          ),
        );

        // console.log('result1', result);

        const cleanedResult = result.map((r) => {
          console.log('openai result: ', r);
          let newValue = r?.content || r || '';
          try {
            // Try to parse if it's a JSON string (e.g., '"karina"')
            const parsed = JSON.parse(newValue);
            // If it's a string, return it directly
            if (typeof parsed === 'string') {
              return parsed;
            }
            // If it's an object with a 'content' key, return that
            if (parsed && typeof parsed.content === 'string') {
              return parsed.content;
            }
            // If it's an object with a 'text' key, return that
            if (parsed && typeof parsed.text === 'string') {
              return parsed.text;
            }
            // If it's an object with a 'value' key, return that
            if (parsed && typeof parsed.value === 'string') {
              return parsed.value;
            }
            // If it's an object, but not a string, return an empty string (or log a warning)
            console.warn('Unexpected object returned from OpenAI:', parsed);
            return '';
          } catch (error) {
            // If parsing fails, just return the original value (should be a string)
          }
          // Remove wrapping quotes if present (as a fallback)
          if (typeof newValue === 'string' && newValue.startsWith('"') && newValue.endsWith('"')) {
            return newValue.slice(1, -1);
          }
          // If it's still not a string, fallback to empty string
          if (typeof newValue !== 'string') {
            console.warn('Non-string value returned from OpenAI:', newValue);
            return '';
          }
          return newValue;
        });

        // Reconstruct the original array structure from translated chunks
        const reconstructedResult = reconstructFromChunks(cleanedResult, indexMap);

        return Array.isArray(text) ? reconstructedResult : reconstructedResult[0];
      },
      async usage() {
        return client.usage();
      },
    }
  }
}
