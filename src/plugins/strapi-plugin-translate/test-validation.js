#!/usr/bin/env node
'use strict'

// Simple test runner for field validation
const { 
  extractFieldValidationRules, 
  getSchemaValidationRules, 
  validateFieldValue,
  validateTranslatedContent 
} = require('./server/utils/field-validation')

// Mock strapi global
global.strapi = {
  components: {
    'shared.seo': {
      attributes: {
        metaTitle: {
          type: 'string',
          required: true,
          maxLength: 70
        },
        metaDescription: {
          type: 'text',
          required: true,
          maxLength: 250
        },
        keywords: {
          type: 'text'
        }
      }
    },
    'shared.meta-social': {
      attributes: {
        title: {
          type: 'string',
          required: true,
          maxLength: 60
        },
        description: {
          type: 'string',
          required: true,
          maxLength: 65
        }
      }
    }
  },
  contentTypes: {
    'api::page.page': {
      attributes: {
        title: {
          type: 'string',
          required: true
        },
        seo: {
          type: 'component',
          component: 'shared.seo',
          repeatable: false
        },
        content: {
          type: 'dynamiczone',
          components: ['shared.meta-social']
        }
      }
    }
  },
  log: {
    error: console.error,
    warn: console.warn,
    debug: console.debug
  }
}

function runValidationTests() {
  console.log('🧪 Running Field Validation Tests...\n')
  
  let passed = 0
  let failed = 0
  
  function test(name, testFn) {
    try {
      testFn()
      console.log(`✅ ${name}`)
      passed++
    } catch (error) {
      console.log(`❌ ${name}`)
      console.log(`   Error: ${error.message}`)
      failed++
    }
  }
  
  function expect(actual) {
    return {
      toBe: (expected) => {
        if (actual !== expected) {
          throw new Error(`Expected ${expected}, got ${actual}`)
        }
      },
      toEqual: (expected) => {
        if (JSON.stringify(actual) !== JSON.stringify(expected)) {
          throw new Error(`Expected ${JSON.stringify(expected)}, got ${JSON.stringify(actual)}`)
        }
      },
      toBeNull: () => {
        if (actual !== null) {
          throw new Error(`Expected null, got ${actual}`)
        }
      },
      toHaveLength: (length) => {
        if (!actual || actual.length !== length) {
          throw new Error(`Expected length ${length}, got ${actual ? actual.length : 'undefined'}`)
        }
      },
      toContain: (substring) => {
        if (!actual || !actual.includes(substring)) {
          throw new Error(`Expected "${actual}" to contain "${substring}"`)
        }
      }
    }
  }
  
  // Test extractFieldValidationRules
  test('extractFieldValidationRules - maxLength', () => {
    const rules = extractFieldValidationRules({ type: 'string', maxLength: 70 })
    expect(rules).toEqual({ maxLength: 70 })
  })
  
  test('extractFieldValidationRules - minLength', () => {
    const rules = extractFieldValidationRules({ type: 'string', minLength: 5 })
    expect(rules).toEqual({ minLength: 5 })
  })
  
  test('extractFieldValidationRules - no rules', () => {
    const rules = extractFieldValidationRules({ type: 'string' })
    expect(rules).toEqual({})
  })
  
  // Test getSchemaValidationRules
  test('getSchemaValidationRules - page schema with components', () => {
    const schema = strapi.contentTypes['api::page.page']
    const rules = getSchemaValidationRules(schema)
    expect(rules['seo.metaTitle']).toEqual({ maxLength: 70 })
    expect(rules['seo.metaDescription']).toEqual({ maxLength: 250 })
    expect(rules['content.*.title']).toEqual({ maxLength: 60 })
    expect(rules['content.*.description']).toEqual({ maxLength: 65 })
  })
  
  // Test validateFieldValue
  test('validateFieldValue - valid string', () => {
    const result = validateFieldValue('Short title', { maxLength: 70 }, 'title')
    expect(result).toBeNull()
  })
  
  test('validateFieldValue - string exceeding maxLength', () => {
    const longString = 'This is a very long string that exceeds the limit'
    const result = validateFieldValue(longString, { maxLength: 5 }, 'title')
    expect(result).toHaveLength(1)
    expect(result[0].rule).toBe('maxLength')
    expect(result[0].field).toBe('title')
  })
  
  test('validateFieldValue - non-string values', () => {
    expect(validateFieldValue(123, { maxLength: 5 }, 'number')).toBeNull()
    expect(validateFieldValue(null, { maxLength: 5 }, 'null')).toBeNull()
  })
  
  // Test validateTranslatedContent
  test('validateTranslatedContent - valid page content', () => {
    const data = {
      title: 'Valid Page Title',
      seo: {
        metaTitle: 'Valid Meta Title',
        metaDescription: 'Valid meta description'
      }
    }
    const result = validateTranslatedContent(data, 'api::page.page')
    expect(result.isValid).toBe(true)
    expect(result.errors).toHaveLength(0)
  })
  
  test('validateTranslatedContent - invalid SEO metaTitle', () => {
    const data = {
      title: 'Valid Page Title',
      seo: {
        metaTitle: 'This is a very long meta title that definitely exceeds the 70 character limit set for SEO meta titles',
        metaDescription: 'Valid meta description'
      }
    }
    const result = validateTranslatedContent(data, 'api::page.page')
    expect(result.isValid).toBe(false)
    expect(result.errors).toHaveLength(1)
    expect(result.errors[0].field).toBe('seo.metaTitle')
    expect(result.errors[0].rule).toBe('maxLength')
  })
  
  test('validateTranslatedContent - valid dynamic zone', () => {
    const data = {
      title: 'Valid Page Title',
      content: [
        {
          __component: 'shared.meta-social',
          title: 'Valid social title',
          description: 'Valid social description'
        }
      ]
    }
    const result = validateTranslatedContent(data, 'api::page.page')
    expect(result.isValid).toBe(true)
    expect(result.errors).toHaveLength(0)
  })
  
  test('validateTranslatedContent - invalid dynamic zone title', () => {
    const data = {
      title: 'Valid Page Title',
      content: [
        {
          __component: 'shared.meta-social',
          title: 'This is a very long social media title that exceeds the 60 character limit',
          description: 'Valid description'
        }
      ]
    }
    const result = validateTranslatedContent(data, 'api::page.page')
    expect(result.isValid).toBe(false)
    expect(result.errors).toHaveLength(1)
    expect(result.errors[0].field).toBe('content.0.title')
    expect(result.errors[0].rule).toBe('maxLength')
  })
  
  test('validateTranslatedContent - multiple validation errors', () => {
    const data = {
      title: 'Valid Page Title',
      seo: {
        metaTitle: 'This is a very long meta title that definitely exceeds the 70 character limit set for SEO meta titles',
        metaDescription: 'This is an extremely long meta description that definitely exceeds the 250 character limit set for SEO meta descriptions. It contains way too much text and should trigger a validation error when processed by the field validation utility function. Adding even more text to ensure it exceeds the limit.'
      }
    }
    const result = validateTranslatedContent(data, 'api::page.page')
    expect(result.isValid).toBe(false)
    expect(result.errors).toHaveLength(2)
  })
  
  console.log(`\n📊 Test Results: ${passed} passed, ${failed} failed`)
  
  if (failed === 0) {
    console.log('🎉 All tests passed!')
    return true
  } else {
    console.log('💥 Some tests failed!')
    return false
  }
}

if (require.main === module) {
  const success = runValidationTests()
  process.exit(success ? 0 : 1)
}

module.exports = { runValidationTests }
