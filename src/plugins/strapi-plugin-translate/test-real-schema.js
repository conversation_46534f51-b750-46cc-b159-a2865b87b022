#!/usr/bin/env node
'use strict'

// Test with real schema files
const fs = require('fs')
const path = require('path')
const { validateTranslatedContent, getSchemaValidationRules } = require('./server/utils/field-validation')

// Load real schemas
const pageSchema = JSON.parse(fs.readFileSync(path.join(__dirname, '../../api/page/content-types/page/schema.json'), 'utf8'))
const seoComponent = JSON.parse(fs.readFileSync(path.join(__dirname, '../../components/shared/seo.json'), 'utf8'))
const metaSocialComponent = JSON.parse(fs.readFileSync(path.join(__dirname, '../../components/shared/meta-social.json'), 'utf8'))

// Mock strapi global with real schemas
global.strapi = {
  components: {
    'shared.seo': seoComponent,
    'shared.meta-social': metaSocialComponent
  },
  contentTypes: {
    'api::page.page': pageSchema
  },
  log: {
    error: console.error,
    warn: console.warn,
    debug: console.debug
  }
}

function testRealSchema() {
  console.log('🧪 Testing with Real Schema Files...\n')
  
  // Test 1: Extract validation rules from real page schema
  console.log('📋 Extracting validation rules from real page schema...')
  const rules = getSchemaValidationRules(pageSchema)
  console.log('Validation rules found:', JSON.stringify(rules, null, 2))
  
  // Test 2: Valid page content
  console.log('\n✅ Testing valid page content...')
  const validData = {
    title: 'Valid Page Title',
    seo: {
      metaTitle: 'Valid Meta Title (under 70 chars)',
      metaDescription: 'Valid meta description under 250 characters',
      keywords: 'some, keywords'
    }
  }
  
  const validResult = validateTranslatedContent(validData, 'api::page.page')
  console.log('Valid content result:', validResult.isValid ? '✅ PASS' : '❌ FAIL')
  if (!validResult.isValid) {
    console.log('Unexpected errors:', validResult.errors)
  }
  
  // Test 3: Invalid SEO content
  console.log('\n❌ Testing invalid SEO content...')
  const invalidSeoData = {
    title: 'Valid Page Title',
    seo: {
      metaTitle: 'This is a very long meta title that definitely exceeds the 70 character limit set for SEO meta titles in the real schema',
      metaDescription: 'This is an extremely long meta description that definitely exceeds the 250 character limit set for SEO meta descriptions in the real schema. It contains way too much text and should trigger a validation error when processed by the field validation utility function. Adding even more text to ensure it exceeds the limit.',
      keywords: 'some, keywords'
    }
  }
  
  const invalidSeoResult = validateTranslatedContent(invalidSeoData, 'api::page.page')
  console.log('Invalid SEO content result:', !invalidSeoResult.isValid ? '✅ PASS' : '❌ FAIL')
  if (invalidSeoResult.isValid) {
    console.log('Expected validation errors but got none')
  } else {
    console.log('Validation errors found:')
    invalidSeoResult.errors.forEach(error => {
      console.log(`  - ${error.field}: ${error.message}`)
    })
  }
  
  // Test 4: Test with meta-social component if it has validation rules
  if (rules['seo.metaSocial.title'] || rules['seo.metaSocial.*.title']) {
    console.log('\n📱 Testing meta-social component validation...')
    const metaSocialData = {
      title: 'Valid Page Title',
      seo: {
        metaTitle: 'Valid Meta Title',
        metaDescription: 'Valid meta description',
        metaSocial: [
          {
            socialNetwork: 'Facebook',
            title: 'This is a very long social media title that exceeds the limit',
            description: 'This is a very long social media description that exceeds the limit'
          }
        ]
      }
    }
    
    const metaSocialResult = validateTranslatedContent(metaSocialData, 'api::page.page')
    console.log('Meta-social validation result:', !metaSocialResult.isValid ? '✅ PASS' : '⚠️  No validation errors (check if meta-social has length constraints)')
    if (!metaSocialResult.isValid) {
      console.log('Meta-social validation errors:')
      metaSocialResult.errors.forEach(error => {
        console.log(`  - ${error.field}: ${error.message}`)
      })
    }
  }
  
  console.log('\n🎯 Real schema validation test completed!')
  return true
}

if (require.main === module) {
  try {
    testRealSchema()
    console.log('\n✅ All real schema tests completed successfully!')
  } catch (error) {
    console.error('\n❌ Real schema test failed:', error.message)
    console.error(error.stack)
    process.exit(1)
  }
}
