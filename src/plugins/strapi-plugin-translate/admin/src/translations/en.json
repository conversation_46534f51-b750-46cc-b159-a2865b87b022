{"CMEditViewTranslateLocale.translate-success": "Copied and translated from other locale!", "CMEditViewTranslateLocale.translate-failure": "Failed to translate locale", "CMEditViewTranslateLocale.translate-text": "Get Translation", "CMEditViewTranslateLocale.ModalConfirm.content": "Your current content will be erased and filled by the translated content of the selected locale:", "CMEditViewTranslateLocale.submit-text": "Yes, get translation", "plugin.name": "Translate", "Settings.locales.modal.locales.label": "Locales", "popUpWarning.button.cancel": "No, cancel", "translate.error.badRequest": "Bad request to Translate-Provider API", "translate.error.forbidden": "Authorization to Translate-Provider API failed", "translate.error.notFound": "Requested resource not found in Translate-Provider API", "translate.error.payloadTooLarge": "Request size exceeds the limit in Translate-Provider API", "translate.error.uriTooLong": "URI to long in Translate-Provider API", "translate.error.tooManyRequests": "Too many requests to Translate-Provider API", "translate.error.paymentRequired": "Quota of Translate-Provider API is exceeded", "batch-translate.dialog.translate.source-locale-missing": "Source locale is missing", "batch-translate.dialog.translate.title": "Start batch translation", "batch-translate.dialog.cancel.title": "Cancel batch translation", "batch-translate.dialog.pause.title": "Pause batch translation", "batch-translate.dialog.resume.title": "Resume batch translation", "batch-translate.dialog.translate.content": "Translate all entities from one locale to another that do not yet have this specific localization", "batch-translate.dialog.cancel.content": "Cancel translating these entities. You will need to start a new job to continue.", "batch-translate.dialog.pause.content": "Pause translating these entities. You may continue the translation at any point in time.", "batch-translate.dialog.resume.content": "Resume translating these entities.", "batch-translate.dialog.translate.autoPublish.label": "Auto-Publish", "batch-translate.dialog.translate.autoPublish.hint": "Publish translated entities automatically", "batch-translate.dialog.translate.submit-text": "Translate", "batch-translate.dialog.cancel.submit-text": "Confirm", "batch-translate.dialog.pause.submit-text": "Pause", "batch-translate.dialog.resume.submit-text": "Resume", "batch-translate.dialog.update.content": "Select groups of entities to retranslate.", "batch-translate.dialog.update.submit-text": "Update selected entities", "batch-translate.table.entries": "entries", "batch-translate.table.complete.true": "complete", "batch-translate.table.complete.false": "incomplete", "batch-translate.table.job-status.created": "Job created", "batch-translate.table.job-status.setup": "Job setup", "batch-translate.table.job-status.running": "Job running", "batch-translate.table.job-status.paused": "Job paused", "batch-translate.table.job-status.finished": "Job finished", "batch-translate.table.job-status.cancelled": "Job cancelled", "batch-translate.table.job-status.failed": "Job failed", "batch-translate.table.actions.labels.translate": "Translate", "batch-translate.table.actions.labels.cancel": "Cancel", "batch-translate.table.actions.labels.pause": "Pause", "batch-translate.table.actions.labels.resume": "Resume", "batch-translate.table.actions.labels.review-changes": "Review Changes", "batch-update.was-updated": "was updated", "batch-update.select": "select", "batch-update.select-all": "select all", "batch-update.dismiss": "dismiss selected", "batch-update.out-of-date": "translations may be outdated", "errors.unknown-error": "Unknown error", "usage.title": "API usage", "usage.failed-to-load": "failed to load usage data", "usage.characters-used": "characters used", "usage.estimatedUsage": "This action is expected to increase your API usage by: ", "usage.estimatedUsageExceedsQuota": "This action is expected to exceed your API Quota", "batch-update.dialog.title": "start batch updating", "batch-update.dialog.content": "All localizations of selected elements will be replaced by translations from the selected source locale.", "batch-update.sourceLocale": "source locale", "batch-update.dialog.submit-text": "start", "validation-changes.modal.title": "Validation Changes Review", "validation-changes.modal.no-changes": "No validation changes found for this content type and locale.", "validation-changes.modal.summary.title": "Summary", "validation-changes.modal.summary.total-changes": "Total Changes", "validation-changes.modal.summary.by-type": "Changes by Type", "validation-changes.modal.summary.by-field": "Changes by Field", "validation-changes.modal.changes.title": "Detailed Changes", "validation-changes.modal.changes.field": "Field", "validation-changes.modal.changes.type": "Type", "validation-changes.modal.changes.description": "Description", "validation-changes.modal.changes.original-length": "Original Length", "validation-changes.modal.changes.new-length": "New Length", "validation-changes.modal.severity.high": "High", "validation-changes.modal.severity.medium": "Medium", "validation-changes.modal.severity.low": "Low", "validation-changes.modal.type.truncation": "Truncation", "validation-changes.modal.type.padding": "Padding", "validation-changes.modal.type.correction": "Correction", "validation-changes.modal.close": "Close", "validation-changes.modal.clear-changes": "Clear Changes", "validation-changes.modal.clear-changes.confirm": "Are you sure you want to clear all validation changes for this content type and locale?", "validation-changes.modal.field.click-to-navigate": "Click to navigate to this field in the content entry", "validation-changes.error.failed-to-load": "Failed to load validation changes", "validation-changes.error.failed-to-clear": "Failed to clear validation changes", "validation-changes.success.cleared": "Validation changes cleared successfully"}