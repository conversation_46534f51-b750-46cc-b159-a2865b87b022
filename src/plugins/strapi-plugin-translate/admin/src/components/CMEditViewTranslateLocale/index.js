import React, { useState, useEffect } from 'react'
import PropTypes from 'prop-types'
import styled from 'styled-components'
import get from 'lodash/get'
import { useSelector } from 'react-redux'
import { useIntl } from 'react-intl'
import { Dialog, DialogBody, DialogFooter } from '@strapi/design-system/Dialog'
import { Select, Option } from '@strapi/design-system/Select'
import { Button } from '@strapi/design-system/Button'
import { Tooltip } from '@strapi/design-system/Tooltip'
import { Box } from '@strapi/design-system/Box'
import { Divider } from '@strapi/design-system/Divider'
import { Typography } from '@strapi/design-system/Typography'
import { Flex } from '@strapi/design-system/Flex'
import { Stack } from '@strapi/design-system/Stack'
import { ExclamationMarkCircle } from '@strapi/icons';
import {
  useCMEditViewDataManager,
  useNotification,
  useQueryParams,
  CheckPermissions,
  request,
} from '@strapi/helper-plugin'
import _ from 'lodash'
import { getTrad } from '../../utils'
import permissions from '../../permissions'
import useUsage from '../../Hooks/useUsage'
import parseRelations from './utils/parse-relations'
import flattenEntity from './utils/flattenEntity'

const CenteredTypography = styled(Typography)`
  text-align: center;
`

// Alternative using theme colors
const ThemeOrangeIcon = styled(ExclamationMarkCircle)`
  && {
    color: ${({ theme }) => theme.colors.warning600} !important;
    fill: ${({ theme }) => theme.colors.warning600} !important;
    
    path, g, * {
      fill: ${({ theme }) => theme.colors.warning600} !important;
    }
  }
`

const CMEditViewTranslateLocale = () => {
  const [{ query }] = useQueryParams()
  const locales = useSelector((state) => state.i18n_locales.locales)
  const { layout, modifiedData, slug } = useCMEditViewDataManager()
  const readPermissions =
    (useSelector(
      (state) => state.rbacProvider.collectionTypesRelatedPermissions
    )[slug] || [])['plugin::content-manager.explorer.read'] || []

  const defaultLocale = locales.find((loc) => loc.isDefault)
  const currentLocale = get(query, 'plugins.i18n.locale', defaultLocale.code)
  const hasI18nEnabled = get(
    layout,
    ['pluginOptions', 'i18n', 'localized'],
    false
  )
  const localizations = get(modifiedData, 'localizations', [])

  if (!hasI18nEnabled || !localizations.length) {
    return null
  }

  return (
    <CheckPermissions permissions={permissions.translate}>
      <Content
        {...{
          appLocales: locales,
          currentLocale,
          localizations,
          readPermissions,
        }}
      />
    </CheckPermissions>
  )
}

const Content = ({
  appLocales,
  currentLocale,
  localizations,
  readPermissions,
}) => {
  const { allLayoutData, initialData, slug, onChange } =
    useCMEditViewDataManager()

  const options = appLocales
    .filter(({ code }) => {
      return (
        code !== currentLocale &&
        localizations.map(({ locale }) => locale).includes(code) &&
        readPermissions.some(({ properties }) =>
          get(properties, 'locales', []).includes(code)
        )
      )
    })
    .map(({ name, code }) => {
      return {
        label: name,
        value: localizations.find(({ locale }) => code === locale).id,
      }
    })

  const toggleNotification = useNotification()
  const { formatMessage } = useIntl()
  const [isLoading, setIsLoading] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const [value, setValue] = useState(options[0]?.value || '')
  const [expectedCost, setExpectedCost] = useState(undefined)

  const { usage, estimateUsage, hasUsageInformation } = useUsage()

  useEffect(() => {
    if (isOpen && hasUsageInformation) {
      estimateUsage({
        id: value,
        contentTypeUid: slug,
        sourceLocale: localizations.find(({ id }) => id == value).locale,
      }).then(setExpectedCost, () => { })
    }
  }, [value, isOpen, slug, localizations, estimateUsage, hasUsageInformation])

  const handleConfirmCopyLocale = async () => {
    if (!value) {
      handleToggle()

      return
    }

    const translateURL = `/translate/translate`

    setIsLoading(true)
    try {
      const { locale: sourceLocale } = localizations.find(
        ({ id }) => id == value
      )
      const translatedData = await request(translateURL, {
        method: 'POST',
        body: {
          id: value,
          sourceLocale,
          targetLocale: currentLocale,
          contentTypeUid: slug,
        },
      })

      const parsedData = parseRelations(translatedData, allLayoutData);
      [
        'createdBy',
        'updatedBy',
        'publishedAt',
        'id',
        'createdAt',
        'updatedAt',
      ].forEach((key) => {
        _.unset(parsedData, key)

        if (!initialData[key]) return
        parsedData[key] = initialData[key]
      });

      const flattenedData = flattenEntity(parsedData, allLayoutData)

      for (const key in flattenedData) {
        if (Object.hasOwnProperty.call(flattenedData, key)) {
          let { value, type } = flattenedData[key]

          if (type) {
            if (type === 'json') {
              value = JSON.stringify(value)
            }
            onChange({ target: { name: key, value, type } })
          }
        }
      }

      toggleNotification({
        type: 'success',
        message: {
          id: getTrad('CMEditViewTranslateLocale.translate-success'),
          defaultMessage: 'Copied and translated from other locale!',
        },
      })
    } catch (err) {
      console.error(err)

      toggleNotification({
        type: 'warning',
        message: {
          id: getTrad(err.response?.data?.error?.message),
          defaultMessage: 'Failed to translate locale',
        },
      })
    } finally {
      setIsLoading(false)
      handleToggle()
    }
  }

  const handleChange = (value) => {
    setValue(value)
  }

  const handleToggle = () => {
    setIsOpen((prev) => !prev)
  }

  return (
    <Box paddingTop={6}>
      <Typography variant="sigma" textColor="neutral600">
        {formatMessage({
          id: getTrad('plugin.name-openai'),
          defaultMessage: 'OPENAI TRANSLATION',
        })}
      </Typography>
      <Box paddingTop={2} paddingBottom={6}>
        <Divider />
      </Box>
      <Tooltip
        description={formatMessage({
          id: getTrad('CMEditViewTranslateLocale.translate-tooltip'),
          defaultMessage: 'Get Translation',
        })}
      >
        <Button
          fullWidth
          variant={"secondary"}
          onClick={handleToggle}
        >
          {formatMessage({
            id: getTrad('CMEditViewTranslateLocale.translate-text'),
            defaultMessage: 'Get Translation',
          })}
        </Button>
      </Tooltip>
      {isOpen && (
        <Dialog
          onClose={handleToggle}
          title="Confirmation"
          isOpen={isOpen}
          variant="info"
        >
          <DialogBody icon={<ThemeOrangeIcon />}>
            <Stack spacing={2}>
              <Flex justifyContent="center">
                <CenteredTypography id="confirm-description">
                  {formatMessage({
                    id: getTrad(
                      'CMEditViewTranslateLocale.ModalConfirm.content'
                    ),
                    defaultMessage:
                      'Your current content will be erased and filled by the translated content of the selected locale:',
                  })}
                </CenteredTypography>
              </Flex>
              <Box>
                <Select
                  label={formatMessage({
                    id: getTrad('Settings.locales.modal.locales.label'),
                  })}
                  onChange={handleChange}
                  value={value}
                >
                  {options.map(({ label, value }) => {
                    return (
                      <Option key={value} value={value}>
                        {label}
                      </Option>
                    )
                  })}
                </Select>
              </Box>
            </Stack>
          </DialogBody>
          <DialogFooter
            startAction={
              <Button onClick={handleToggle} variant="tertiary">
                {formatMessage({
                  id: 'popUpWarning.button.cancel',
                  defaultMessage: 'No, cancel',
                })}
              </Button>
            }
            endAction={
              <Button
                variant="success"
                onClick={handleConfirmCopyLocale}
                loading={isLoading}
              >
                {formatMessage({
                  id: getTrad('CMEditViewTranslateLocale.submit-text'),
                  defaultMessage: 'Yes, get translation and fill in',
                })}
              </Button>
            }
          />
        </Dialog>
      )}
    </Box>
  )
}

Content.propTypes = {
  appLocales: PropTypes.arrayOf(
    PropTypes.shape({
      code: PropTypes.string.isRequired,
      name: PropTypes.string,
    })
  ).isRequired,
  currentLocale: PropTypes.string.isRequired,
  localizations: PropTypes.array.isRequired,
  readPermissions: PropTypes.array.isRequired,
}

export default CMEditViewTranslateLocale
