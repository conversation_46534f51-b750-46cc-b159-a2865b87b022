import React, { useState, useEffect } from 'react'
import { useIntl } from 'react-intl'
import { useNotification } from '@strapi/helper-plugin'
import { useHistory } from 'react-router-dom'
import {
  ModalLayout,
  ModalHeader,
  ModalBody,
  ModalFooter,
} from '@strapi/design-system/ModalLayout'
import { Button } from '@strapi/design-system/Button'
import { Typography } from '@strapi/design-system/Typography'
import { Stack } from '@strapi/design-system/Stack'
import { Flex } from '@strapi/design-system/Flex'
import { Box } from '@strapi/design-system/Box'
import { Badge } from '@strapi/design-system/Badge'
import { Table, Thead, Tbody, Tr, Td, Th } from '@strapi/design-system/Table'
import { Divider } from '@strapi/design-system/Divider'
import { EmptyStateLayout } from '@strapi/design-system/EmptyStateLayout'
import { Loader } from '@strapi/design-system/Loader'
import ExclamationMarkCircle from '@strapi/icons/ExclamationMarkCircle'
import EmptyDocuments from '@strapi/icons/EmptyDocuments'
import ExternalLink from '@strapi/icons/ExternalLink'
import PropTypes from 'prop-types'
import { getTrad } from '../../utils'
import { fetchValidationChanges, clearValidationChanges } from '../../utils/validationChangesApi'
import './ValidationChangesModal.css'

/**
 * ValidationChangesModal Component
 *
 * Displays validation changes made during translation in a user-friendly modal.
 * Shows summary statistics, detailed change information, and allows clearing changes.
 *
 * Features:
 * - Summary view with change counts by type and field
 * - Detailed table showing all modifications
 * - Severity indicators (High/Medium/Low)
 * - Clear changes functionality
 * - Empty state handling
 * - Accessible modal implementation
 */

const ValidationChangesModal = ({
  isOpen,
  onClose,
  contentType,
  locale,
  onChangesCleared
}) => {
  const { formatMessage } = useIntl()
  const toggleNotification = useNotification()
  const history = useHistory()
  const [validationChanges, setValidationChanges] = useState(null)
  const [loading, setLoading] = useState(false)
  const [clearing, setClearing] = useState(false)

  useEffect(() => {
    if (isOpen && contentType && locale) {
      console.log(`🔍 ValidationChangesModal opened for ${contentType}:${locale}`)
      loadValidationChanges()
    }
  }, [isOpen, contentType, locale])

  const loadValidationChanges = async () => {
    setLoading(true)
    try {
      const response = await fetchValidationChanges(contentType, locale)
      console.log('📋 Loaded validation changes data:', response.data)
      setValidationChanges(response.data)
    } catch (error) {
      console.error('Failed to load validation changes:', error)
      toggleNotification({
        type: 'warning',
        message: {
          id: getTrad('validation-changes.error.failed-to-load'),
          defaultMessage: 'Failed to load validation changes',
        },
      })
      setValidationChanges(null)
    } finally {
      setLoading(false)
    }
  }

  const handleClearChanges = async () => {
    if (!window.confirm(formatMessage({
      id: getTrad('validation-changes.modal.clear-changes.confirm'),
      defaultMessage: 'Are you sure you want to clear all validation changes for this content type and locale?',
    }))) {
      return
    }

    setClearing(true)
    try {
      await clearValidationChanges(contentType, locale)
      toggleNotification({
        type: 'success',
        message: {
          id: getTrad('validation-changes.success.cleared'),
          defaultMessage: 'Validation changes cleared successfully',
        },
      })
      setValidationChanges(null)
      if (onChangesCleared) {
        onChangesCleared(contentType, locale)
      }
    } catch (error) {
      console.error('Failed to clear validation changes:', error)
      toggleNotification({
        type: 'warning',
        message: {
          id: getTrad('validation-changes.error.failed-to-clear'),
          defaultMessage: 'Failed to clear validation changes',
        },
      })
    } finally {
      setClearing(false)
    }
  }

  const handleFieldNavigation = (change) => {
    // Extract entity ID from the specific change (for accumulated changes) or fallback to main entityId
    const entityId = change?.entityId || validationChanges?.entityId

    if (!entityId || !contentType || !locale) {
      console.warn('Missing navigation data:', { entityId, contentType, locale, change })
      return
    }

    // Close the modal first
    onClose()

    // Navigate to the Content Manager for the specific entry
    const navigationPath = `/content-manager/collection-types/${contentType}/${entityId}?plugins[i18n][locale]=${locale}`

    console.log(`🧭 Navigating to: ${navigationPath} for change:`, change.field)

    // Use setTimeout to ensure modal closes before navigation
    setTimeout(() => {
      history.push(navigationPath)
    }, 100)
  }

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'high':
        return 'danger500'
      case 'medium':
        return 'warning500'
      case 'low':
        return 'success500'
      default:
        return 'neutral500'
    }
  }

  const ClickableField = ({ change }) => {
    const [isHovered, setIsHovered] = useState(false)

    return (
      <Box
        as="button"
        onClick={() => handleFieldNavigation(change)}
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
        style={{
          cursor: 'pointer',
          border: 'none',
          background: 'transparent',
          padding: '4px 8px',
          borderRadius: '4px',
          transition: 'all 0.2s ease',
          backgroundColor: isHovered ? '#f6f6f9' : 'transparent',
          textAlign: 'left',
          width: '100%'
        }}
        title={formatMessage({
          id: getTrad('validation-changes.modal.field.click-to-navigate'),
          defaultMessage: 'Click to navigate to this field in the content entry',
        })}
      >
        <Flex alignItems="center" gap={2}>
          <Typography
            textColor={isHovered ? 'primary700' : 'primary600'}
            fontWeight={isHovered ? 'semiBold' : 'normal'}
            style={{
              textDecoration: isHovered ? 'underline' : 'none',
              transition: 'all 0.2s ease'
            }}
          >
            {change.field}
          </Typography>
          <ExternalLink
            width="14px"
            height="14px"
            style={{
              color: isHovered ? '#4945ff' : '#8e8ea9',
              transition: 'color 0.2s ease'
            }}
          />
        </Flex>
      </Box>
    )
  }

  const getTypeDisplayName = (type) => {
    return formatMessage({
      id: getTrad(`validation-changes.modal.type.${type}`),
      defaultMessage: type,
    })
  }

  const renderSummary = () => {
    if (!validationChanges || !validationChanges.hasChanges) {
      return null
    }

    // Use the direct properties from the backend response
    const totalChanges = validationChanges.totalChanges || validationChanges.changes?.length || 0
    const changesByType = validationChanges.changesByType || {}

    console.log('📊 Rendering summary with:', {
      totalChanges,
      changesByType,
      validationChangesKeys: Object.keys(validationChanges)
    })

    return (
      <Box>
        <Typography variant="beta" marginBottom={3}>
          {formatMessage({
            id: getTrad('validation-changes.modal.summary.title'),
            defaultMessage: 'Summary',
          })}
        </Typography>

        <Stack spacing={2}>
          <Flex justifyContent="space-between">
            <Typography>
              {formatMessage({
                id: getTrad('validation-changes.modal.summary.total-changes'),
                defaultMessage: 'Total Changes',
              })}:
            </Typography>
            <Badge backgroundColor="primary500" textColor="neutral100">
              {totalChanges}
            </Badge>
          </Flex>

          {changesByType && Object.keys(changesByType).length > 0 && (
            <Box>
              <Typography variant="delta" marginBottom={2}>
                {formatMessage({
                  id: getTrad('validation-changes.modal.summary.by-type'),
                  defaultMessage: 'Changes by Type',
                })}:
              </Typography>
              <Flex wrap="wrap" gap={2}>
                {Object.entries(changesByType).map(([type, changes]) => (
                  <Badge key={type} backgroundColor="neutral200" textColor="neutral800">
                    {getTypeDisplayName(type)}: {changes.length}
                  </Badge>
                ))}
              </Flex>
            </Box>
          )}
        </Stack>
      </Box>
    )
  }

  const renderChangesTable = () => {
    if (!validationChanges || !validationChanges.hasChanges) {
      return null
    }

    const changes = validationChanges.changes || []

    if (changes.length === 0) {
      return null
    }

    return (
      <Box>
        <Typography variant="beta" marginBottom={3}>
          {formatMessage({
            id: getTrad('validation-changes.modal.changes.title'),
            defaultMessage: 'Detailed Changes',
          })}
        </Typography>
        
        <Table colCount={5} rowCount={changes.length + 1}>
          <Thead>
            <Tr>
              <Th>
                <Typography variant="sigma">
                  {formatMessage({
                    id: getTrad('validation-changes.modal.changes.field'),
                    defaultMessage: 'Field',
                  })}
                </Typography>
              </Th>
              <Th>
                <Typography variant="sigma">
                  {formatMessage({
                    id: getTrad('validation-changes.modal.changes.type'),
                    defaultMessage: 'Type',
                  })}
                </Typography>
              </Th>
              <Th>
                <Typography variant="sigma">
                  {formatMessage({
                    id: getTrad('validation-changes.modal.changes.original-length'),
                    defaultMessage: 'Original Length',
                  })}
                </Typography>
              </Th>
              <Th>
                <Typography variant="sigma">
                  {formatMessage({
                    id: getTrad('validation-changes.modal.changes.new-length'),
                    defaultMessage: 'New Length',
                  })}
                </Typography>
              </Th>
              <Th>
                <Typography variant="sigma">
                  {formatMessage({
                    id: getTrad('validation-changes.modal.changes.description'),
                    defaultMessage: 'Description',
                  })}
                </Typography>
              </Th>
            </Tr>
          </Thead>
          <Tbody>
            {changes.map((change, index) => (
              <Tr key={index}>
                <Td>
                  <ClickableField change={change} />
                </Td>
                <Td>
                  <Badge 
                    backgroundColor={getSeverityColor(change.severity || 'medium')}
                    textColor="neutral100"
                  >
                    {getTypeDisplayName(change.type)}
                  </Badge>
                </Td>
                <Td>
                  <Typography textColor="neutral800">
                    {change.originalLength || '-'}
                  </Typography>
                </Td>
                <Td>
                  <Typography textColor="neutral800">
                    {change.newLength || '-'}
                  </Typography>
                </Td>
                <Td>
                  <Typography textColor="neutral800">
                    {change.message}
                  </Typography>
                </Td>
              </Tr>
            ))}
          </Tbody>
        </Table>
      </Box>
    )
  }

  const renderContent = () => {
    if (loading) {
      return (
        <Box className="loading-state">
          <Loader>Loading validation changes...</Loader>
        </Box>
      )
    }

    if (!validationChanges) {
      return (
        <Box className="empty-state">
          <EmptyStateLayout
            icon={<EmptyDocuments width="10rem" height="10rem" />}
            content={formatMessage({
              id: getTrad('validation-changes.modal.no-data'),
              defaultMessage: 'No validation changes data available.',
            })}
          />
        </Box>
      )
    }

    if (!validationChanges.hasChanges) {
      return (
        <Box className="empty-state">
          <EmptyStateLayout
            icon={<EmptyDocuments width="10rem" height="10rem" />}
            content={formatMessage({
              id: getTrad('validation-changes.modal.no-changes'),
              defaultMessage: 'No validation changes found for this content type and locale.',
            })}
          />
        </Box>
      )
    }

    // Additional safety check for changes array
    const changes = validationChanges.changes || []
    if (changes.length === 0) {
      return (
        <Box className="empty-state">
          <EmptyStateLayout
            icon={<EmptyDocuments width="10rem" height="10rem" />}
            content={formatMessage({
              id: getTrad('validation-changes.modal.empty-changes'),
              defaultMessage: 'Validation changes data is empty.',
            })}
          />
        </Box>
      )
    }

    return (
      <>
        {/* Fixed summary section */}
        <Box className="summary-section">
          {renderSummary()}
          <Divider />
        </Box>

        {/* Scrollable table section */}
        <Box className="table-section">
          {renderChangesTable()}
        </Box>
      </>
    )
  }

  if (!isOpen) {
    return null
  }

  return (
    <ModalLayout 
      onClose={onClose}
      labelledBy="validation-changes-modal-title"
      className="validation-changes-modal"
    >
      <ModalHeader>
        <Flex alignItems="center" gap={2}>
          <ExclamationMarkCircle color="warning500" />
          <Typography 
            variant="beta" 
            fontWeight="bold" 
            id="validation-changes-modal-title"
          >
            {formatMessage({
              id: getTrad('validation-changes.modal.title'),
              defaultMessage: 'Validation Changes Review',
            })}
          </Typography>
        </Flex>
      </ModalHeader>
      
      <ModalBody className="modal-body">
        <Box className="content-container">
          {renderContent()}
        </Box>
      </ModalBody>
      
      <ModalFooter
        startActions={
          validationChanges && validationChanges.hasChanges && (
            <Button 
              onClick={handleClearChanges} 
              variant="danger-light"
              loading={clearing}
            >
              {formatMessage({
                id: getTrad('validation-changes.modal.clear-changes'),
                defaultMessage: 'Clear Changes',
              })}
            </Button>
          )
        }
        endActions={
          <Button onClick={onClose} variant="tertiary">
            {formatMessage({
              id: getTrad('validation-changes.modal.close'),
              defaultMessage: 'Close',
            })}
          </Button>
        }
      />
    </ModalLayout>
  )
}

ValidationChangesModal.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  contentType: PropTypes.string,
  locale: PropTypes.string,
  onChangesCleared: PropTypes.func,
}

export default ValidationChangesModal
