/* ValidationChangesModal Custom Styling */

/* Target the Strapi ModalLayout component for validation changes modal */
.validation-changes-modal {
  /* Ensure modal is properly sized and positioned */
  width: 90vw !important;
  height: 85vh !important;
  max-width: 1400px !important;
  max-height: 900px !important;
  min-width: 800px !important;
  min-height: 600px !important;
}

/* Ensure the modal content fills the available space */
.validation-changes-modal [role="dialog"] {
  width: 100% !important;
  height: 100% !important;
  max-width: none !important;
  max-height: none !important;
  margin: 0 !important;
}

/* Style the modal body for proper content layout */
.validation-changes-modal .modal-body {
  height: calc(100% - 140px) !important; /* Account for header and footer */
  overflow: hidden !important;
  padding: 24px !important;
  display: flex !important;
  flex-direction: column !important;
}

/* Ensure the content container uses full height */
.validation-changes-modal .content-container {
  height: 100% !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

/* Fixed summary section */
.validation-changes-modal .summary-section {
  flex-shrink: 0 !important;
  margin-bottom: 24px !important;
  padding-bottom: 16px !important;
}

/* Scrollable table section */
.validation-changes-modal .table-section {
  flex: 1 !important;
  overflow: auto !important;
  min-height: 0 !important; /* Important for flex child to be scrollable */
  border: 1px solid #eaeaea !important;
  border-radius: 4px !important;
  padding: 16px !important;
}

/* Responsive design for smaller screens */
@media (max-width: 1200px) {
  .validation-changes-modal {
    width: 95vw !important;
    height: 90vh !important;
    min-width: 700px !important;
    min-height: 500px !important;
  }
}

@media (max-width: 768px) {
  .validation-changes-modal {
    width: 98vw !important;
    height: 95vh !important;
    min-width: 400px !important;
    min-height: 400px !important;
  }
  
  .validation-changes-modal .modal-body {
    padding: 16px !important;
  }
  
  .validation-changes-modal .table-section {
    padding: 12px !important;
  }
}

@media (max-width: 480px) {
  .validation-changes-modal {
    width: 100vw !important;
    height: 100vh !important;
    min-width: 320px !important;
    min-height: 400px !important;
  }
}

/* Ensure table is properly styled within the modal */
.validation-changes-modal table {
  width: 100% !important;
  border-collapse: collapse !important;
}

/* Style table headers for better visibility */
.validation-changes-modal th {
  /*background-color: #f6f6f9 !important;*/
  border-bottom: 2px solid #eaeaea !important;
  padding: 12px 8px !important;
  text-align: left !important;
  font-weight: 600 !important;
}

/* Style table cells for better readability */
.validation-changes-modal td {
  padding: 12px 8px !important;
  border-bottom: 1px solid #f6f6f9 !important;
  vertical-align: top !important;
}

/* Style the empty state to be centered */
.validation-changes-modal .empty-state {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100% !important;
  min-height: 300px !important;
}

/* Loading state styling */
.validation-changes-modal .loading-state {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  height: 100% !important;
  min-height: 300px !important;
}

/* Ensure proper spacing for modal elements */
.validation-changes-modal .modal-header {
  padding: 24px !important;
  border-bottom: 1px solid #eaeaea !important;
  background-color: #ffffff !important;
}

.validation-changes-modal .modal-footer {
  padding: 24px !important;
  border-top: 1px solid #eaeaea !important;
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  background-color: #ffffff !important;
}

/* Fix any overflow issues */
.validation-changes-modal * {
  box-sizing: border-box !important;
}

/* Ensure backdrop is properly styled */
.validation-changes-modal-backdrop {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background-color: rgba(0, 0, 0, 0.5) !important;
  z-index: 999 !important;
}

/* Improve table row hover effects */
.validation-changes-modal tbody tr:hover {
  background-color: #f8f9fa !important;
}

/* Style badges for better visibility */
.validation-changes-modal .badge {
  font-size: 0.75rem !important;
  font-weight: 600 !important;
  padding: 4px 8px !important;
  border-radius: 4px !important;
}

/* Ensure proper spacing for summary badges */
.validation-changes-modal .summary-section .badge {
  margin-right: 8px !important;
  margin-bottom: 8px !important;
}

/* Improve clickable field styling */
.validation-changes-modal .clickable-field {
  transition: all 0.2s ease !important;
}

.validation-changes-modal .clickable-field:hover {
  background-color: #f6f6f9 !important;
  border-radius: 4px !important;
}

/* Ensure proper z-index for modal */
.validation-changes-modal {
  z-index: 1000 !important;
}

/* Improve accessibility with focus styles */
.validation-changes-modal button:focus,
.validation-changes-modal [role="button"]:focus {
  outline: 2px solid #4945ff !important;
  outline-offset: 2px !important;
}

/* Ensure proper contrast for text */
.validation-changes-modal .typography {
  color: #32324d !important;
}

/* Improve table responsiveness */
@media (max-width: 768px) {
  .validation-changes-modal table {
    font-size: 0.875rem !important;
  }
  
  .validation-changes-modal th,
  .validation-changes-modal td {
    padding: 8px 4px !important;
  }
}
