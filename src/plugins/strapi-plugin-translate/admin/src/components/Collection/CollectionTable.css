/* Sticky header for Strapi Collection Table */

.strapi-sticky-table {
  width: 100%;
  border-collapse: collapse;
}

.strapi-sticky-table thead th {
  position: sticky;
  top: 0;
  z-index: 2;
}

.strapi-sticky-table tbody {
  display: block;
  max-height: 800px; /* Adjust as needed */
  overflow-y: auto;
  scrollbar-width: thin;              /* Firefox */
  scrollbar-color: #888 #222;         /* Firefox */
}

.strapi-sticky-table thead,
.strapi-sticky-table tbody tr {
  display: table;
  width: 100%;
  table-layout: fixed;
}

.strapi-sticky-table th,
.strapi-sticky-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #ddd;
  box-sizing: border-box;
}

/* Chrome, Edge, Safari */
.strapi-sticky-table tbody::-webkit-scrollbar {
  width: 8px;
  background: #222;
  border-radius: 8px;
}

.strapi-sticky-table tbody::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 8px;
}

.strapi-sticky-table tbody::-webkit-scrollbar-thumb:hover {
  background: #555;
} 