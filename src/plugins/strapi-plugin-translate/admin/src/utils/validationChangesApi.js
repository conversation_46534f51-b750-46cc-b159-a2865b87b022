import { request } from '@strapi/helper-plugin'

/**
 * API utility functions for validation changes
 */

/**
 * Fetch validation changes for a specific content type and locale
 * @param {string} contentType - The content-type UID
 * @param {string} locale - The target locale
 * @returns {Promise<object>} - API response with validation changes
 */
export const fetchValidationChanges = async (contentType, locale) => {
  console.log(`📡 Fetching validation changes for ${contentType}:${locale}`)

  const response = await request('/translate/validation-changes', {
    method: 'GET',
    params: {
      contentType,
      locale,
    },
  })

  console.log(`📡 Response for ${contentType}:${locale}:`, response)
  return response
}

/**
 * Fetch all validation changes for a specific content type across all locales
 * @param {string} contentType - The content-type UID
 * @returns {Promise<object>} - API response with validation changes by locale
 */
export const fetchAllValidationChangesForContentType = async (contentType) => {
  console.log(`📡 Fetching all validation changes for content type: ${contentType}`)

  const response = await request('/translate/validation-changes/content-type', {
    method: 'GET',
    params: {
      contentType,
    },
  })

  console.log(`📡 All changes response for ${contentType}:`, response)
  return response
}

/**
 * Clear validation changes for a specific content type and locale
 * @param {string} contentType - The content-type UID
 * @param {string} locale - The target locale
 * @returns {Promise<object>} - API response
 */
export const clearValidationChanges = async (contentType, locale) => {
  const response = await request('/translate/validation-changes/clear', {
    method: 'POST',
    body: {
      contentType,
      locale,
    },
  })
  return response
}

/**
 * Get validation changes summary for all content types
 * @returns {Promise<object>} - API response with summary
 */
export const fetchValidationChangesSummary = async () => {
  const response = await request('/translate/validation-changes/summary', {
    method: 'GET',
  })
  return response
}

/**
 * Check if validation changes exist for a content type and locale
 * @param {string} contentType - The content-type UID
 * @param {string} locale - The target locale
 * @returns {Promise<boolean>} - True if changes exist
 */
export const hasValidationChanges = async (contentType, locale) => {
  try {
    const response = await fetchValidationChanges(contentType, locale)
    return response.data && response.data.hasChanges
  } catch (error) {
    // If not found or error, assume no changes
    return false
  }
}

/**
 * Local storage utilities for validation changes
 */
const STORAGE_KEY_PREFIX = 'strapi-translate-validation-changes'
const STORAGE_INDEX_KEY = 'strapi-translate-validation-changes-index'

/**
 * Save validation changes to localStorage
 * @param {string} contentType - The content-type UID
 * @param {string} locale - The target locale
 * @param {object} changes - The validation changes data
 */
export const saveValidationChangesToStorage = (contentType, locale, changes) => {
  const key = `${STORAGE_KEY_PREFIX}:${contentType}:${locale}`
  try {
    const dataToStore = {
      ...changes,
      savedAt: new Date().toISOString(),
      version: '1.0', // For future compatibility
      contentType,
      locale
    }
    localStorage.setItem(key, JSON.stringify(dataToStore))

    // Also maintain an index of all stored validation changes for cleanup
    updateValidationChangesIndex(contentType, locale, true)

    console.log(`💾 Saved validation changes to localStorage: ${key}`)
  } catch (error) {
    console.warn('Failed to save validation changes to localStorage:', error)
  }
}

/**
 * Load validation changes from localStorage
 * @param {string} contentType - The content-type UID
 * @param {string} locale - The target locale
 * @returns {object|null} - Saved validation changes or null
 */
export const loadValidationChangesFromStorage = (contentType, locale) => {
  const key = `${STORAGE_KEY_PREFIX}:${contentType}:${locale}`
  try {
    const stored = localStorage.getItem(key)
    return stored ? JSON.parse(stored) : null
  } catch (error) {
    console.warn('Failed to load validation changes from localStorage:', error)
    return null
  }
}

/**
 * Clear validation changes from localStorage
 * @param {string} contentType - The content-type UID
 * @param {string} locale - The target locale
 */
export const clearValidationChangesFromStorage = (contentType, locale) => {
  const key = `${STORAGE_KEY_PREFIX}:${contentType}:${locale}`
  try {
    localStorage.removeItem(key)
    // Update the index to remove this entry
    updateValidationChangesIndex(contentType, locale, false)
    console.log(`🗑️ Cleared validation changes from localStorage: ${key}`)
  } catch (error) {
    console.warn('Failed to clear validation changes from localStorage:', error)
  }
}

/**
 * Get all validation changes from localStorage for a content type
 * @param {string} contentType - The content-type UID
 * @returns {object} - Object with locale keys and their validation changes
 */
export const getAllValidationChangesFromStorage = (contentType) => {
  const result = {}
  const prefix = `${STORAGE_KEY_PREFIX}:${contentType}:`
  
  try {
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(prefix)) {
        const locale = key.substring(prefix.length)
        const stored = localStorage.getItem(key)
        if (stored) {
          result[locale] = JSON.parse(stored)
        }
      }
    }
  } catch (error) {
    console.warn('Failed to load validation changes from localStorage:', error)
  }
  
  return result
}

/**
 * Clear all validation changes from localStorage for a content type
 * @param {string} contentType - The content-type UID
 * @returns {number} - Number of entries cleared
 */
export const clearAllValidationChangesFromStorage = (contentType) => {
  const prefix = `${STORAGE_KEY_PREFIX}:${contentType}:`
  let clearedCount = 0
  
  try {
    const keysToRemove = []
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key && key.startsWith(prefix)) {
        keysToRemove.push(key)
      }
    }
    
    keysToRemove.forEach(key => {
      localStorage.removeItem(key)
      clearedCount++
    })
  } catch (error) {
    console.warn('Failed to clear validation changes from localStorage:', error)
  }
  
  return clearedCount
}

/**
 * Sync validation changes between server and localStorage
 * @param {string} contentType - The content-type UID
 * @param {string} locale - The target locale
 * @returns {Promise<object|null>} - Synced validation changes
 */
export const syncValidationChanges = async (contentType, locale) => {
  console.log(`🔄 Syncing validation changes for ${contentType}:${locale}`)

  try {
    // Try to fetch from server first
    const serverResponse = await fetchValidationChanges(contentType, locale)
    console.log(`📡 Server response for ${contentType}:${locale}:`, serverResponse)

    if (serverResponse.data) {
      if (serverResponse.data.hasChanges) {
        console.log(`✅ Found ${serverResponse.data.changes?.length || 0} validation changes on server`)
        // Save to localStorage for offline access
        saveValidationChangesToStorage(contentType, locale, serverResponse.data)
        return serverResponse.data
      } else {
        console.log(`📭 No validation changes found on server for ${contentType}:${locale}`)
        // Clear localStorage if server has no changes
        clearValidationChangesFromStorage(contentType, locale)
        return null
      }
    }
  } catch (error) {
    console.warn(`❌ Failed to fetch validation changes from server for ${contentType}:${locale}:`, error)
  }

  // Fallback to localStorage
  console.log(`💾 Falling back to localStorage for ${contentType}:${locale}`)
  const localData = loadValidationChangesFromStorage(contentType, locale)
  console.log(`💾 LocalStorage data for ${contentType}:${locale}:`, localData)
  return localData
}

/**
 * Update the validation changes index for tracking stored data
 * @param {string} contentType - The content-type UID
 * @param {string} locale - The target locale
 * @param {boolean} add - Whether to add (true) or remove (false) from index
 */
export const updateValidationChangesIndex = (contentType, locale, add) => {
  try {
    const indexKey = STORAGE_INDEX_KEY
    let index = {}

    // Load existing index
    const existingIndex = localStorage.getItem(indexKey)
    if (existingIndex) {
      index = JSON.parse(existingIndex)
    }

    // Initialize content type if it doesn't exist
    if (!index[contentType]) {
      index[contentType] = {}
    }

    if (add) {
      // Add to index
      index[contentType][locale] = {
        storedAt: new Date().toISOString(),
        key: `${STORAGE_KEY_PREFIX}:${contentType}:${locale}`
      }
    } else {
      // Remove from index
      delete index[contentType][locale]

      // Clean up empty content type entries
      if (Object.keys(index[contentType]).length === 0) {
        delete index[contentType]
      }
    }

    // Save updated index
    localStorage.setItem(indexKey, JSON.stringify(index))
  } catch (error) {
    console.warn('Failed to update validation changes index:', error)
  }
}

/**
 * Get all stored validation changes keys for cleanup
 * @returns {Array} - Array of storage keys
 */
export const getAllStoredValidationChangesKeys = () => {
  try {
    const indexKey = STORAGE_INDEX_KEY
    const existingIndex = localStorage.getItem(indexKey)

    if (!existingIndex) {
      return []
    }

    const index = JSON.parse(existingIndex)
    const keys = []

    Object.values(index).forEach(contentTypeData => {
      Object.values(contentTypeData).forEach(localeData => {
        keys.push(localeData.key)
      })
    })

    return keys
  } catch (error) {
    console.warn('Failed to get stored validation changes keys:', error)
    return []
  }
}

/**
 * Clear all validation changes for a specific content type
 * @param {string} contentType - The content-type UID
 */
export const clearAllValidationChangesForContentType = (contentType) => {
  try {
    const indexKey = STORAGE_INDEX_KEY
    const existingIndex = localStorage.getItem(indexKey)

    if (!existingIndex) {
      return
    }

    const index = JSON.parse(existingIndex)

    if (index[contentType]) {
      // Remove all localStorage entries for this content type
      Object.values(index[contentType]).forEach(localeData => {
        localStorage.removeItem(localeData.key)
      })

      // Remove from index
      delete index[contentType]

      // Save updated index
      localStorage.setItem(indexKey, JSON.stringify(index))

      console.log(`🗑️ Cleared all validation changes for content type: ${contentType}`)
    }
  } catch (error) {
    console.warn('Failed to clear all validation changes for content type:', error)
  }
}

/**
 * Check if validation changes exist in storage for a content type and locale
 * @param {string} contentType - The content-type UID
 * @param {string} locale - The target locale
 * @returns {boolean} - True if validation changes exist in storage
 */
export const hasValidationChangesInStorage = (contentType, locale) => {
  try {
    const data = loadValidationChangesFromStorage(contentType, locale)
    return data && data.hasChanges && data.changes && data.changes.length > 0
  } catch (error) {
    console.warn('Failed to check validation changes in storage:', error)
    return false
  }
}
