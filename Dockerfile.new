FROM node:20.17.0 AS builder

WORKDIR /app

COPY package.json package-lock.json ./

COPY ./src/plugins ./src/plugins

RUN npm install --production

COPY . .

ENV NODE_ENV=production

RUN npm run build


FROM node:20.17.0-alpine AS production

WORKDIR /app

COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/package-lock.json ./package-lock.json
#COPY --from=builder /app/src ./src
COPY --from=builder ./src/plugins ./src/plugins
# If you have a 'config' directory with runtime configurations, copy it:
# COPY --from=builder /app/config ./config

# Expose the port Strapi listens on
EXPOSE 1337

# Define the command to run the Strapi application
CMD ["npm", "start"]
